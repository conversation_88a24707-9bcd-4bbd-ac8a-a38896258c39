import { Activity99516CreateOrUpdateRequest, Activity99516CreateOrUpdateResponse } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 达能海外中奖名单B端控制器
 * @summary 创建活动
 * @request POST:/99516/createActivity
 */
export const createActivity = (
  request: Activity99516CreateOrUpdateRequest,
): Promise<Activity99516CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99516/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 达能海外中奖名单B端控制器
 * @summary 修改活动
 * @request POST:/99516/updateActivity
 */
export const updateActivity = (
  request: Activity99516CreateOrUpdateRequest,
): Promise<Activity99516CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99516/updateActivity',
    method: 'post',
    data: request,
  });
};
