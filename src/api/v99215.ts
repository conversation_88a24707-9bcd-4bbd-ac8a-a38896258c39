import {
  Activity99215CreateOrUpdateRequest,
  Activity99215CreateOrUpdateResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 京造邀请有礼
 * @summary 创建活动
 * @request POST:/99215/createActivity
 */
export const createActivity = (
  request: Activity99215CreateOrUpdateRequest,
): Promise<Activity99215CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99215/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 京造邀请有礼
 * @summary 查询活动信息
 * @request POST:/99215/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/99215/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 京造邀请有礼
 * @summary 修改活动
 * @request POST:/99215/updateActivity
 */
export const updateActivity = (
  request: Activity99215CreateOrUpdateRequest,
): Promise<Activity99215CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99215/updateActivity',
    method: 'post',
    data: request,
  });
};
