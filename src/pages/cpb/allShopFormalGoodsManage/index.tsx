import React, { useEffect, useState } from 'react';
import styles from './style.module.scss';
import LzPanel from '@/components/LzPanel';
import { Button, Dialog, Field, Form, Input, Message, Upload } from '@alifd/next';
import { activityEditDisabled, deepCopy, downloadExcel } from '@/utils';
import { config } from 'ice';
import CONST from '@/utils/constant';
import SkuList from '@/components/SkuList';
import { getNotExcludedSku } from '@/api/sku';
import { allShopSkuSkuTemplateExport, allShopSkuSkuInfo } from '@/api/v1000429085';
// import { dataOrderRecord } from '@/api/v90006';

const defaultPage = {
  pageNum: 1,
  pageSize: 30,
  total: 0,
};

export default () => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [loading, setLoading] = useState(false);
  const [skuFileList, setSkuFileList] = useState<any>([]);
  const [skuFileName, setSkuFileName] = useState('');
  const [skuList, setSkuList] = useState<any>([]);

  const loadData = (query: any): void => {
    setLoading(true);
    allShopSkuSkuInfo(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].nums = i + 1;
        }
        setSkuList(res.records as any[]);
        // setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  const downloadTemplate = async () => {
    try {
      const data: any = await allShopSkuSkuTemplateExport();
      downloadExcel(data, '全店正装sku导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const handlePreview = async (data) => {
    const skuIdsList = skuList.map((item) => item.skuId);
    // // 调接口查商品
    // await getAllSku({ skuIds: skuIdsList })
    //   .then((res: any): void => {
    //     console.log(res);
    //     setData({ skuList: res });
    //   })
    //   .catch((e) => {
    //     console.log(e.message);
    //   });
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div className={styles.crmContaine}>
      <LzPanel title="全店正装商品管理" subTitle="">
        <div style={{ minHeight: '350px' }}>
          <div className={styles.btnGroup}>
            <Button type="primary" text className={styles.download} onClick={downloadTemplate}>
              下载模板
            </Button>
            <Upload
              disabled={activityEditDisabled()}
              action={`${config.baseURL}/importSkuExcel`}
              name="file"
              method="post"
              headers={{
                token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
                prd: localStorage.getItem(CONST.LZ_SSO_PRD),
              }}
              // ref={saveUploaderRef}
              value={skuFileList}
              fileNameRender={(file) => <span>{skuFileName}</span>}
              limit={1}
              listType="text"
              accept=".xls,.xlsx"
              onClick={() => {

              }}
              onChange={(info) => {
                if (info.length) {
                  if (info[0].size > 5 * 1024 * 1024) {
                    Message.error('文件大小不能超过5M');
                    return;
                  }
                }
                console.log(info[0].name, 'info[0].name');
                setSkuFileList(info);
                setSkuFileName(info[0].name);
              }}
              onError={(res) => {
                if (res.state === 'error') {
                  if (res.response?.message) {
                    Message.error(res.response?.message);
                  } else {
                    Message.error('文件错误，请上传正确的文件');
                  }
                }
              }}
              onRemove={(file) => {
                setSkuFileList([]);
              }}
              onSuccess={(res) => {
                if (res.response.code === 200) {
                  setSkuList(res.response.data);
                  Dialog.success({
                    title: '导入结果',
                    content: (
                      <div>
                        <p>导入成功，共导入 {res.response.data.length} 个全店正装SKU</p>
                      </div>
                    ),
                    onOk: () => {
                      console.log('导入成功');
                    },
                  });
                } else if (res.response?.message) {
                  setSkuFileList([]);
                  Message.error(res.response?.message);
                } else {
                  setSkuFileList([]);
                  Message.error('文件错误，请上传正确的文件');
                }
              }}
              style={{ marginBottom: 10 }}
            >
              <div className="next-upload-drag">
                <p className="next-upload-drag-icon">
                  <Button type="primary">上传全店正装商品</Button>
                </p>
                <p className="next-upload-drag-hint">支持xls类型的文件</p>
              </div>
            </Upload>
          </div>
          <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
            <Form.Item name="skuId" label="skuId">
              <Input placeholder="请输入skuId" />
            </Form.Item>
            <Form.Item colon={false}>
              <Form.Submit type="primary" htmlType="submit">
                查询
              </Form.Submit>
              <Form.Reset
                toDefault
                onClick={() => {
                  const formValue: any = field.getValues();
                  loadData({ ...formValue, ...defaultPage });
                }}
              >
                重置
              </Form.Reset>
            </Form.Item>
          </Form>
          <div>
            <SkuList skuList={skuList} handlePreview={handlePreview} />
          </div>
        </div>
      </LzPanel>
    </div>
  );
};
