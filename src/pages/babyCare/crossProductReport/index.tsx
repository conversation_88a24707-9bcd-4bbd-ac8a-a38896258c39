/**
 * 92017 记录
 */
import React, { useEffect, useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import SKUReport from './components/SKUReport';
import LzDocGuide from '@/components/LzDocGuide';
import { getParams } from '@/utils';

export default () => {
  useEffect(() => {
    // getNum();
  }, []);

  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="跨品活动数据" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="SKU数据" key="1">
            <SKUReport />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
