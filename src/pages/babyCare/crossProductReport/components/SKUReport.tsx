import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Table, Upload } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { exposeExport, importExposeExcel, exposeList, exposeListExport } from '@/api/v92017Data';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayjs from 'dayjs';
import { config } from 'ice';
import CONST from '@/utils/constant';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [infoXlsx, setInfoXlsx] = useState<any>({});
  const [uploaderRef, setUploaderRef] = useState<any>({});
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [fileList, setFileList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    exposeList(query)
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ pageSize, pageNum });
  };
  const exportData = () => {
    // const formValue: any = field.getValues();
    // formValue.activityId = getParams('id');
    exposeListExport({ ...defaultPage }).then((data: any) => downloadExcel(data, 'SKU数据'));
  };

  // 下载模板
  const downLoadFile = () => {
    exposeExport().then((data: any) => {
      downloadExcel(data, `导入SKU模板`);
    });
  };

  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };

  const afterSelect = (info) => {
    console.log('afterSelect callback : ', info);
    setInfoXlsx(info.originFileObj);
    if (
      info.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' &&
      info.type !== 'application/vnd.ms-excel'
    ) {
      Message.error('请上传xlsx类型的文件');
      return false;
    } else {
      return true;
    }
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        {/* <Form.Item name="pin" label="用户pin"> */}
        {/*  <Input placeholder="请输入用户pin" /> */}
        {/* </Form.Item> */}

        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          {/* <Form.Reset */}
          {/*  toDefault */}
          {/*  onClick={() => { */}
          {/*    const formValue: any = field.getValues(); */}
          {/*    loadData({ ...formValue, ...defaultPage }); */}
          {/*  }} */}
          {/* > */}
          {/*  重置 */}
          {/* </Form.Reset> */}
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={downLoadFile}>下载模板</Button>
        <div style={{ display: 'inline-flex', marginLeft: '10px' }}>
          <Upload
            style={{ textOverflow: 'ellipsis', overflow: 'hidden', display: 'block' }}
            action={`${config.baseURL}/92017Data/importExposeExcel`}
            name="file"
            method="post"
            headers={{
              token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
              prd: localStorage.getItem(CONST.LZ_SSO_PRD),
            }}
            value={fileList}
            listType="text"
            ref={saveUploaderRef}
            afterSelect={afterSelect}
            useDataURL
            limit={1}
            onError={(res) => {
              console.log(res);
              if (res.state === 'error') {
                if (res.response?.message) {
                  Message.error(res.response?.message);
                } else {
                  Message.error('文件错误，请上传正确的文件');
                }
              }
            }}
            onSuccess={(res) => {
              console.log(res, 'res');
              if (res.response.code === 200) {
                Message.success('上传成功');
                loadData({ ...defaultPage });
              } else if (res.response?.message) {
                Message.error(res.response?.message);
              } else {
                Message.error('文件错误，请上传正确的文件');
              }
            }}
          >
            <Button className="table-cell-btn" type="secondary">
              上传文件
            </Button>
          </Upload>
        </div>

        <Button onClick={exportData} style={{ marginLeft: '10px' }}>
          导出
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="商品名称" dataIndex="skuName" />
        <Table.Column title="SKU" dataIndex="skuId" />
        <Table.Column title="品牌" dataIndex="brandName" />
        <Table.Column title="一级类目" dataIndex="oneCategory" />
        <Table.Column title="二级类目" dataIndex="twoCategory" />
        <Table.Column title="三级类目" dataIndex="threeCategory" />
        <Table.Column title="是否影分身" dataIndex="isShadowClone" />
        <Table.Column title="上下柜状态" dataIndex="upperLowerStatus" />
        <Table.Column title="上柜时间" dataIndex="upperLowerTime" />
        <Table.Column title="是否赠品" dataIndex="isGift" />
        <Table.Column title="SPU编码" dataIndex="spuCode" />
        <Table.Column title="条形码" dataIndex="barcode" />
        <Table.Column title="商品价格" dataIndex="jdPrice" />
        <Table.Column title="全国近90日收货地商品件数" dataIndex="buyUserNum" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
