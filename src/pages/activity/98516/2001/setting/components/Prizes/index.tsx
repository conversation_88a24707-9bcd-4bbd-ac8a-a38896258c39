import React, { useEffect, useImperative<PERSON>andle, useReducer, useState } from 'react';
import { Button, Dialog, Field, Form, Message, NumberPicker, Radio, Table } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData, PRIZE_INFO, PrizeInfo } from '../../../util';
import styles from './index.module.scss';
import { activityEditDisabled, isDisableSetPrize } from '@/utils';
import { PRIZE_TYPE } from '@/pages/activity/90017/1001/util';
import ChoosePrize from '@/components/ChoosePrize';
import LzDialog from '@/components/LzDialog';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  // 选择奖品
  const [visible, setVisible] = useState(false);
  const [leaderVisible, setLeaderVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [plan, setPlan] = useState<any[]>([]);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const newPrizeListLength = formData.prizeList.length;
    // 生成默认奖品列表
    const list1: PrizeInfo[] = [...formData.prizeList];
    // 补齐奖品
    if (!newPrizeListLength) {
      list1.push({ ...PRIZE_INFO, type: 2 });
    }
    setData({
      prizeList: list1.length ? list1 : formData.prizeList,
    });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      console.log('prize=========');
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  const onNewPrizeChange = (data): boolean | void => {
    if (activityEditDisabled() && data.prizeName !== '谢谢参与') {
      if (data.sendTotalCount < defaultValue.prizeList[target].sendTotalCount) {
        Message.error(`发放份数不能小于${defaultValue.prizeList[target].sendTotalCount}份`);
        return false;
      }
    }
    // 更新指定index 奖品信息
    formData.prizeList[target] = {
      ...data,
      type: 2,
      prizeKey: data.planId || data.promoId || data.prizeKey,
      prizeName: data.prizeName || data.planName,
      peopleNum: formData.prizeList[target].peopleNum,
      sortId: target,
    };
    const list = formData.prizeList.map((item) => item.prizeKey);
    setPlan(list);
    setData(formData);
    setVisible(false);
  };

  const onCancel = (): void => {
    setVisible(false);
  };
  return (
    <div>
      <LzPanel title="奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="奖品列表" required>
            <Table dataSource={formData.prizeList} style={{ marginTop: 10 }}>
              <Table.Column
                title="奖品名称"
                dataIndex="prizeName"
                cell={(_, index, row) => <div>{row.prizeName}</div>}
              />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                        ? Number(row.unitPrice).toFixed(2)
                        : ''}
                  </div>
                )}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    <FormItem style={{ marginBottom: '0' }} disabled={isDisableSetPrize(formData.prizeList, index)}>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.prizeList[index];
                          if (!row.prizeName) {
                            row = null;
                          }
                          setEditValue(row || null);
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                      </Button>
                      {formData.prizeList[index].prizeType > 0 && !activityEditDisabled() && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            if (_.prizeType) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空该奖品？',
                                onOk: () => {
                                  formData.prizeList.splice(index, 1, PRIZE_INFO);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      )}
                    </FormItem>
                  )}
                />
            </Table>
          </FormItem>
          <LzDialog
            title={false}
            visible={visible}
            footer={false}
            onClose={() => setVisible(false)}
            style={{ width: '670px' }}
          >
            <ChoosePrize
              formData={formData}
              editValue={editValue}
              hasLimit={false}
              hasProbability={false}
              defaultTarget={8}
              typeList={[8]}
              onChange={onNewPrizeChange}
              onCancel={onCancel}
              planList={plan}
            />
          </LzDialog>
        </Form>
      </LzPanel>
    </div>
  );
};
