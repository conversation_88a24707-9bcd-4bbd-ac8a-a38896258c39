import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, DatePicker2, Field, Message } from '@alifd/next';
import constant from '@/utils/constant';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import format from '@/utils/format';

const FormItem = Form.Item;

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  // 以活动开始时间往前推半年
  const dateString = formData.startTime;
  const startTime = new Date(dateString);
  const sixMonthsAgo = new Date(startTime);
  sixMonthsAgo.setDate(startTime.getDate());
  sixMonthsAgo.setMonth(startTime.getMonth() - 6);
  const AnotherSixMonthsAgo = new Date(sixMonthsAgo);
  AnotherSixMonthsAgo.setDate(AnotherSixMonthsAgo.getDate() + 1);

  // // 禁用时间
  // const disabledDate = (current) => {
  //   // 禁用活动开始日期之后以及半年前之前的日期
  //   return current && (current < sixMonthsAgo || current > new Date(formData.startTime));
  // };

  const disabledDate = (current) => {
    const startDate = new Date(formData.startTime);
    const date = new Date('2022-07-23');
    // 禁用条件：当前日期在活动开始日期之后或者在2022年7月24日之前
    return current && (current > startDate || current < date);
  };

  // 日期改变，处理提交数据
  const onDataRangeChange = (orderStartTime): void => {
    if (format.formatDateTimeDayjs(orderStartTime) >= format.formatDateTimeDayjs(formData.startTime)) {
      Message.error('计算订单开始时间不能大于或等于活动开始时间');
      const date1 = Date.parse(orderStartTime) / 1000;
      const date2 = Date.parse(formData.startTime) / 1000;
      const difference = date1 - date2 + 1;
      // 拿到应该退回到的秒数
      const num = new Date(orderStartTime).getSeconds() - difference;
      const val = new Date(orderStartTime).setSeconds(num);
      setData({
        orderStartTime: format.formatDateTimeDayjs(new Date(val)),
      });
      return;
    }
    setData({
      orderStartTime: format.formatDateTimeDayjs(new Date(orderStartTime)),
    });
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="参与规则设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="参与规则">
            <div style={{ color: 'red' }}>
              <div>1.入会后成功购买过1笔及以上正装产品订单（订单状态完成）</div>
              <div>2.会员复购有礼同一用户只享受一次</div>
            </div>
          </FormItem>
          <FormItem
            label="计算订单开始时间"
            required
            requiredMessage="请选择计算订单开始时间"
            extra={
              <div className="next-form-item-help">
                注：订单计算开始时间默认为当前店铺可追溯最早的订单时间，设置时间需要大于等于最早的订单时间，当前可追溯的最早订单时间为：
                2022-07-24 00:00:00
                {/* {format.formatDateTimeDayjs(AnotherSixMonthsAgo)} */}
              </div>
            }
          >
            <DatePicker2
              style={{ width: '150px' }}
              name="orderStartTime"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.orderStartTime}
              onChange={onDataRangeChange}
              disabledDate={disabledDate}
            />
            <span style={{ marginLeft: '10px' }}>至活动开始时间，存在1笔及1笔以上的订单，且订单状态为完成</span>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
