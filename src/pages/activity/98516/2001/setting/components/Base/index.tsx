/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Input, DatePicker2, Field, Dialog, Checkbox } from '@alifd/next';
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';

const FormItem = Form.Item;

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  // const changeOrderTime = () => {
  //   // 以活动开始时间往前推半年
  //   const dateString = formData.startTime;
  //   const startTime = new Date(dateString);
  //   const sixMonthsAgo = new Date(startTime);
  //   sixMonthsAgo.setMonth(startTime.getMonth() - 6);
  //   sixMonthsAgo.setDate(startTime.getDate() + 1);
  //   setData({ orderStartTime: format.formatDateTimeDayjs(new Date(sixMonthsAgo)) });
  // };

  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setData({
      rangeDate,
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };
  // useEffect(() => {
  //   changeOrderTime();
  // }, [formData.startTime]);
  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };
  /*
   * 查看官方设置示例
   */
  const showDetails = () => {
    setVisible(true);
  };
  const [visible, setVisible] = useState(false);
  const closeDialog = () => {
    setVisible(false);
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="活动基本信息">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="活动名称" required requiredMessage="请输入活动名称" disabled={false}>
            <Input
              value={formData.activityName}
              placeholder="请输入活动名称"
              name="activityName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(activityName) => setData({ activityName })}
            />
          </FormItem>
          <FormItem
            label="活动时间"
            required
            requiredMessage="请选择活动时间"
            extra={<div className="next-form-item-help">注：活动时间需设置在软件的订购有效期内</div>}
          >
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <DateRangeDuration
              rangeDate={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
            />
            <div>
              <span style={{ color: 'red' }}>
                请填写令牌所绑定的总价促销活动的时间，务必与官方后台设置的信息保持一致，否则易产生客诉
              </span>
              <span style={{ color: '#2877FD', marginLeft: '20px', cursor: 'pointer' }} onClick={showDetails}>
                查看官方设置示例
              </span>
            </div>
            <Dialog width="900px" height="400px" v2 title="" footer={false} visible={visible} onClose={closeDialog}>
              <img
                style={{ width: '795px', height: '302px', margin: '20px 0 0 30px' }}
                src="//img10.360buyimg.com/imgzone/jfs/t1/244374/16/1917/39948/65951a70F2f99a3ff/e0ee81b9d4cecbba.png"
                alt=""
              />
            </Dialog>
          </FormItem>
          <FormItem label="活动门槛" required>
            <Checkbox defaultChecked disabled>
              店铺会员
            </Checkbox>
            {/* <LzThreshold formData={formData} field={field} setData={setData} apply={[1]} applyGrade={[]} /> */}
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
