import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { userAllActRecordPage, userAllActRecordPageExport } from '@/api/v92017Data';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [timeRange, setTimeRange] = useState([
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ]);
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.dateRange = timeRange;
    userAllActRecordPage(query)
      .then((res: any): void => {
        setTableData([res] as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    userAllActRecordPageExport(formValue).then((data: any) => downloadExcel(data, '活动SKU数据报表'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        {/* <FormItem name="dateRange" label="活动时间"> */}
        {/*  <RangePicker */}
        {/*    hasClear={false} */}
        {/*    defaultValue={timeRange} */}
        {/*    showTime */}
        {/*    value={timeRange} */}
        {/*    onChange={(val: any) => setTimeRange([val[0].format('YYYY-MM-DD'), val[1].format('YYYY-MM-DD')])} */}
        {/*    format="YYYY-MM-DD" */}
        {/*  /> */}
        {/* </FormItem> */}
        <FormItem colon={false}>
          {/* <Form.Submit type="primary" htmlType="submit"> */}
          {/*  查询 */}
          {/* </Form.Submit> */}
          {/* <Form.Reset */}
          {/*  toDefault */}
          {/*  onClick={() => { */}
          {/*    const formValue: any = field.getValues(); */}
          {/*    // setTimeRange([ */}
          {/*    //   dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'), */}
          {/*    //   dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'), */}
          {/*    // ]); */}
          {/*    loadData({ ...formValue, ...defaultPage }); */}
          {/*  }} */}
          {/* > */}
          {/*  重置 */}
          {/* </Form.Reset> */}
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="日期" width={150} dataIndex="dateTime" />
        <Table.Column title="UV付款人数" width={100} dataIndex="uvBuyUserNum" />
        <Table.Column title="UV付款金额" width={100} dataIndex="uvBuyUserPrice" />
        <Table.Column title="UV APRU" width={100} dataIndex="uvArpu" />
        <Table.Column title="UV付款 三级品类总数" width={100} dataIndex="uvBuyCategoryNum" />
        <Table.Column title="人均三级品类数" width={100} dataIndex="uvBuyCategoryArpu" />
      </Table>
      {/* <LzPagination */}
      {/*  pageNum={pageInfo.pageNum} */}
      {/*  pageSize={pageInfo.pageSize} */}
      {/*  total={pageInfo.total} */}
      {/*  onChange={handlePage} */}
      {/* /> */}
    </div>
  );
};
