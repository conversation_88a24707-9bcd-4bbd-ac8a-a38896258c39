/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 大转盘
 */
import React, { useReducer, useEffect, useState } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Icon, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';
import LzDialog from '@/components/LzDialog';
import EditHotZone from '../EditHotZone';
import { deepCopy } from '@/utils';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [hotVisible, setHotVisible] = React.useState(false);
  const [editHotData, setEditHotData] = useState({
    bg: '',
    hotZoneList: [],
  });
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    // console.log('setForm======', obj);
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };

  /**
   * 添加热区
   */
  const addHotZone = () => {
    if (!formData.hotZoneSetting.bg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setEditHotData({
      bg: formData.hotZoneSetting.prizeBg,
      hotZoneList: formData.hotZoneSetting.hotZoneList,
    });
    setHotVisible(true);
  };

  const onCloseHotZone = () => {
    setHotVisible(false);
  };

  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);

  const addSkuClick = () => {
    console.log('addSkuClick');
    formData.qy2BgItemBgArr.push({
      skuImg: '',
      position: formData.qy2BgItemBgArr + 1,
    });
    setForm(formData);
  };
  return (
    <div className={styles.wheel}>
      <LzPanel title="权益设置">
        <Form {...formLayout} className={styles.form}>

          <Form.Item label="权益一设置">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={688}
                  height={320}
                  value={formData.qy1Bg}
                  onChange={(qy1Bg) => {
                    setForm({ qy1Bg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度688px，高度320px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ qy1Bg: defaultValue?.qy1Bg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="权益二背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={688}
                  value={formData.qy2Bg}
                  onChange={(qy2Bg) => {
                    setForm({ qy2Bg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：建议宽度688px,高度不小于320px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ qy2Bg: defaultValue?.qy2Bg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {formData.qy2BgItemBgArr &&
            formData.qy2BgItemBgArr.length > 0 &&
            formData.qy2BgItemBgArr.map((item, index) => {
              return (
                <Form.Item key={index} label={`权益二奖品${index + 1}奖品图`}>
                  <Grid.Row>
                    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                      <LzImageSelector
                        width={626}
                        height={160}
                        value={item.skuImg}
                        onChange={(skuImg) => {
                          formData.qy2BgItemBgArr[index].skuImg = skuImg;
                          setForm(formData);
                        }}
                      />
                    </Form.Item>
                    <Form.Item style={{ marginBottom: 0 }}>
                      <div className={styles.tip}>
                        <p>图片尺寸：626*160px</p>
                        <p>图片大小：不超过1M</p>
                        <p>图片格式：JPG、JPEG、PNG</p>
                      </div>
                      <div>
                        <Button
                          type="primary"
                          text
                          onClick={() => {
                            formData.qy2BgItemBgArr.splice(index, 1);
                            setForm(formData);
                          }}
                        >
                          删除
                        </Button>
                        <Button
                          type="primary"
                          text
                          onClick={() => {
                            const defaultValue1 = deepCopy(defaultValue?.qy2BgItemBgArr[index]);
                            formData.qy2BgItemBgArr[index].skuImg = defaultValue1?.skuImg;
                            setForm(formData);
                          }}
                        >
                          重置
                        </Button>
                        {index !== 0 && (
                          <Button
                            type="primary"
                            text
                            onClick={() => {
                              formData.qy2BgItemBgArr.splice(
                                index - 1,
                                1,
                                ...formData.qy2BgItemBgArr.splice(index, 1, formData.qy2BgItemBgArr[index - 1]),
                              );
                              setForm(formData);
                            }}
                          >
                            上移
                          </Button>
                        )}
                        {index !== formData.qy2BgItemBgArr.length - 1 && (
                          <Button
                            type="primary"
                            text
                            onClick={() => {
                              formData.qy2BgItemBgArr.splice(
                                index,
                                1,
                                ...formData.qy2BgItemBgArr.splice(index + 1, 1, formData.qy2BgItemBgArr[index]),
                              );
                              setForm(formData);
                            }}
                          >
                            下移
                          </Button>
                        )}
                      </div>
                    </Form.Item>
                  </Grid.Row>
                </Form.Item>
              );
            })}
          <Form.Item label={' '} colon={false}>
            <Button
              type="primary"
              onClick={() => {
                addSkuClick();
              }}
            >
              新增权益二奖品图
            </Button>
          </Form.Item>

          <Form.Item label="权益三设置">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  value={formData.hotZoneSetting.bg}
                  width={688}
                  onChange={(bg) => {
                    const hotZoneSetting = { ...formData.hotZoneSetting, bg };
                    setForm({ hotZoneSetting });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：建议宽度688px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      const hotZoneSetting = { ...formData.hotZoneSetting, bg: defaultValue.hotZoneSetting.bg };
                      setForm({ hotZoneSetting });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>




          <Form.Item label="图片热区" required key="hot">
            <Button
              style={{ margin: '20px auto', width: '20%', color: '#3399FF' }}
              type="normal"
              onClick={() => {
                addHotZone();
              }}
            >
              <Icon className="iconfont icon-a-1plus" style={{ marginRight: 10 }} />
              {`添加热区`}
            </Button>
          </Form.Item>
          <LzDialog
            title={'编辑热区'}
            visible={hotVisible}
            footer={false}
            onClose={() => setHotVisible(false)}
            style={{ width: '750px' }}
          >
            <EditHotZone
              // dataIndex={0}
              // dispatch={(val: any) => {
              //   setForm({
              //     hotZoneList: val,
              //   });
              //   setHotVisible(false);
              // }}
              data={formData.hotZoneSetting}
              onChange={(hotZoneList) => {
                formData.hotZoneSetting.hotZoneList = hotZoneList;
                setForm(formData);
                setHotVisible(false);
              }}
              onClose={() => onCloseHotZone()}
            />
          </LzDialog>
        </Form>
      </LzPanel>
    </div>
  );
};
