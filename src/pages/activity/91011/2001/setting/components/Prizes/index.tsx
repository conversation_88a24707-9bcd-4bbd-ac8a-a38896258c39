/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Message, NumberPicker } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrizeForDZ from '@/components/ChoosePrizeForDZ';
import { activityEditDisabled, deepCopy, getParams, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import dayjs from 'dayjs';
// import { getPrizeRemain } from '@/api/v91011';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  const [multipleVisible, setMultipleVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [setQyType, setSetQyType] = useState(1); // 1 设置权益1奖品 2设置权益2奖品
  const [prizeTypeList, setPrizeTypeList] = useState<any[]>([]);
  // 编辑时最小发放数量
  const [sendTotalCountMin, setSendTotalCountMin] = useState(1);
  const [currentorderAmount, setCurrentorderAmount] = useState('');
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  const onMultiplePrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    // console.log('选择权益奖品', defaultValue.prizeListTwo, data);
    const prizeListAll = [...formData.prizeListOne, ...formData.prizeListTwo];
    const seriesKeys = prizeListAll.map((innerItem: any) => {
      return innerItem.prizeKey;
    }).filter((it) => it != 0 && it != null && it != '' && it != undefined);
    if (setQyType === 1) {
      if (formData.prizeListOne[target] && formData.prizeListOne[target].prizeKey === data.prizeKey) {
        //编辑的奖品没有修改奖品prizeKey点击确认
      } else {
        if (seriesKeys.includes(data.prizeKey)) {
          Message.error('该奖品已存在，不能设置重复奖品~');
          return;
        }
      }
    }
    if (setQyType === 2) {
      if (formData.prizeListTwo[target] && formData.prizeListTwo[target].prizeKey === data.prizeKey) {
        //编辑的奖品没有修改奖品prizeKey点击确认
      } else {
        if (seriesKeys.includes(data.prizeKey)) {
          Message.error('该奖品已存在，不能设置重复奖品~');
          return;
        }
      }
    }
    if (setQyType === 1) {
      data.equityType = 1;
      data.sortId = 1;
      if (activityEditDisabled()) {
        if (data.prizeType === 4 && data.editType === 'edit') {
          // 积分
          if (data.sendTotalCount < sendTotalCountMin) {
            Message.error(`发放份数不能小于${sendTotalCountMin}份`);
            return false;
          }
        } else if (data.editType === 'edit' && data.prizeKey === defaultValue.prizeListOne[target].prizeKey) {
          if (data.sendTotalCount < sendTotalCountMin) {
            Message.error(`发放份数不能小于${sendTotalCountMin}份`);
            return false;
          }
        }
      }
      formData.prizeListOne[target] = data;
    } else if (setQyType === 2) {
      data.equityType = 2;
      if (data.prizeType === 4) {
        data.prizeKey = data.prizeKey ? data.prizeKey : dayjs().valueOf();
      }
      // const maxValue = numbers.length > 0 ? Math.max(...numbers) : "数组为空";
      // const maxSortId = Math.max(...deepCopy(formData.prizeListTwo).map((item: any) => item.sortId));
      // console.log(maxSortId, 'maxSortId======='); // 输出: 4
      data.sortId = target + 1;
      data.orderAmount = currentorderAmount;
      if (activityEditDisabled() && defaultValue.prizeListTwo[target]) {
        if (data.prizeType === 4 && data.editType === 'edit') {
          // 积分
          if (data.sendTotalCount < sendTotalCountMin) {
            Message.error(`发放份数不能小于${sendTotalCountMin}份`);
            return false;
          }
        } else {
          if (data.editType === 'edit' && data.prizeKey === defaultValue.prizeListTwo[target].prizeKey) {
            if (data.sendTotalCount < sendTotalCountMin) {
              Message.error(`发放份数不能小于${sendTotalCountMin}份`);
              return false;
            }
          }
        }

      }
      formData.prizeListTwo[target] = data;
    }
    setData(formData);
    setMultipleVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始权益1奖品列表长度
    const prizeListOneLength = formData.prizeListOne.length;
    // 生成默认奖品列表
    const list3: PrizeInfo[] = [...formData.prizeListOne];
    for (let i = 0; i < 1 - prizeListOneLength; i++) {
      PRIZE_INFO.equityType = 1; // 权益1奖品
      list3.push(deepCopy(PRIZE_INFO));
    }

    // 初始权益2奖品列表长度
    const prizeListTwoLength = formData.prizeListTwo.length;
    // 生成默认奖品列表
    const list4: PrizeInfo[] = [...formData.prizeListTwo];
    for (let i = 0; i < 1 - prizeListTwoLength; i++) {
      PRIZE_INFO.equityType = 2; // 权益2奖品
      PRIZE_INFO.sortId = 0;
      list4.push(deepCopy(PRIZE_INFO));
    }
    setData({
      prizeListOne: list3.length ? list3 : formData.prizeListOne,
      prizeListTwo: list4.length ? list4 : formData.prizeListTwo,
    });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onMultipleCancel = (): void => {
    setMultipleVisible(false);
  };

  const editPrizes = async (prize: any, index: number) => {
    let row = deepCopy(prize[index]);
    if (row.prizeName === '谢谢参与' || !row.prizeName) {
      row = null;
    } else if (getParams('type') === 'edit') {
      if (activityEditDisabled() && defaultValue.prizeListOne[target]) {
        setSendTotalCountMin(defaultValue.prizeListOne[target].sendTotalCount ?? 1);
      }
    }
    setEditValue(row || null);
    setTarget(index);
  };

  // 首购权益1奖品修改
  const editMultiplePrize = async (index: number) => {
    setSetQyType(1);
    setPrizeTypeList([1]);
    await editPrizes(formData.prizeListOne, index);
    // console.log(formData.prizeListOne, index, 'zxczxczc==========');
    setMultipleVisible(true);
  };

  const editPrizesTwo = async (prize: any, index: number) => {
    let row = deepCopy(prize[index]);
    if (row.prizeName === '谢谢参与' || !row.prizeName) {
      row = null;
    } else if (getParams('type') === 'edit') {
      if (activityEditDisabled() && defaultValue.prizeListTwo[target]) {
        setSendTotalCountMin(defaultValue.prizeListTwo[target].sendTotalCount ?? 1);
      }
    }
    setEditValue(row || null);
    setTarget(index);
  };

  // 首购权益1奖品修改
  const editMultiplePrizeTwo = async (index: number, itemData: any) => {
    setSetQyType(2); // 选择权益二的奖品
    setCurrentorderAmount(itemData.orderAmount);
    setPrizeTypeList([2, 1, 3, 4]); // 实物 礼品卡 权益C端没有对应设计，不能选择
    await editPrizesTwo(formData.prizeListTwo, index);
    setMultipleVisible(true);
  };


  // 新增权益2奖品
  const addPrizeClick2 = () => {
    const list3: PrizeInfo[] = [...formData.prizeListTwo];
    PRIZE_INFO.equityType = 2;
    PRIZE_INFO.orderAmount = '1';
    // PRIZE_INFO.sortId = list3.length + 1;
    list3.push(deepCopy(PRIZE_INFO));
    setData({
      prizeListTwo: list3.length ? list3 : formData.prizeListTwo,
    });
  };
  return (
    <div>
      <LzPanel title="奖项设置" subTitle={
        <div style={{ color: '#999' }}>
          如果需要更改奖品请先清空奖品后再去编辑
        </div>}>

        <Form {...formItemLayout} field={field}>
          <FormItem label="首购权益一设置" required>
            <Table dataSource={formData.prizeListOne}>
              <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />

              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem disabled={isDisableSetPrize(formData.prizeListOne, index)}>
                    <Button text type="primary" onClick={() => editMultiplePrize(index)}>
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {(_.prizeType > 0 || index > 0) && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {

                            if (formData.prizeListOne.length === 1) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空该奖品？',
                                onOk: () => {
                                  PRIZE_INFO.equityType = 1;
                                  formData.prizeListOne.splice(index, 1, deepCopy(PRIZE_INFO));
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                            if (formData.prizeListOne.length > 1) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认删除该奖品？',
                                onOk: () => {
                                  formData.prizeListOne.splice(index, 1);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      </>
                    )}
                  </FormItem>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="首购权益二设置" required>
            <Table dataSource={formData.prizeListTwo}>
              <Table.Column title="领取条件" width={220} cell={(_, index, data) => {
                return (
                  <div>
                    消费
                    <NumberPicker
                      onChange={(v: number) => {
                        // 更新指定index 奖品信息
                        const newFormData = { ...formData };
                        newFormData.prizeListTwo[index].orderAmount = v;
                        setData(newFormData);
                      }}
                      type="inline"
                      name="unitPrice"
                      min={1}
                      precision={1}
                      max={999999}
                      defaultValue={1}
                      key={index}
                      value={data.orderAmount}
                    />
                    元
                  </div>
                )
              }} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />

              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <FormItem disabled={isDisableSetPrize(formData.prizeListTwo, index)}>
                    <Button text type="primary" onClick={() => editMultiplePrizeTwo(index, _)}>
                      <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                    </Button>
                    {(_.prizeType > 0 || formData.prizeListTwo.length > 1) && (
                      <>
                        <Button
                          text
                          type="primary"
                          disabled={activityEditDisabled() && (!_.prizeType || _.prizeType <= 0)}
                          onClick={() => {
                            if (getParams('type') === 'edit') {
                              console.log('edit', _.orderAmount);
                              setCurrentorderAmount(deepCopy(_).orderAmount);
                            } else {
                              setCurrentorderAmount('1');
                            }
                            if (activityEditDisabled()) {
                              // 编辑进行中活动只能清空，不能删除
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空该奖品？',
                                onOk: () => {
                                  PRIZE_INFO.equityType = 2;
                                  PRIZE_INFO.orderAmount = currentorderAmount;
                                  formData.prizeListTwo.splice(index, 1, PRIZE_INFO);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);

                            } else {
                              if (formData.prizeListTwo.length === 1) {
                                Dialog.confirm({
                                  v2: true,
                                  title: '提示',
                                  centered: true,
                                  content: '确认清空该奖品？',
                                  onOk: () => {
                                    PRIZE_INFO.equityType = 2;
                                    PRIZE_INFO.orderAmount = currentorderAmount;
                                    formData.prizeListTwo.splice(index, 1, PRIZE_INFO);
                                    setData(formData);
                                  },
                                  onCancel: () => console.log('cancel'),
                                } as any);
                              }
                              if (formData.prizeListTwo.length > 1) {
                                Dialog.confirm({
                                  v2: true,
                                  title: '提示',
                                  centered: true,
                                  content: '确认删除该奖品？',
                                  onOk: () => {
                                    formData.prizeListTwo.splice(index, 1);
                                    setData(formData);
                                  },
                                  onCancel: () => console.log('cancel'),
                                } as any);
                              }
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      </>
                    )}
                  </FormItem>
                )}
              />
            </Table>
            <Button
              type="primary"
              style={{ marginTop: '10px' }}
              onClick={() => {
                addPrizeClick2();
              }}
            >
              新增权益二奖品
            </Button>
          </FormItem>
        </Form>
      </LzPanel>
      <LzDialog
        title={false}
        visible={multipleVisible}
        footer={false}
        onClose={() => setMultipleVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrizeForDZ
          defaultEditValue={editValue}
          formData={formData}
          editValue={editValue}
          onChange={onMultiplePrizeChange}
          onCancel={onMultipleCancel}
          hasProbability={false}
          hasLimit={false}
          hasShowTime={false}
          typeList={prizeTypeList}
          defaultTarget={prizeTypeList[0]}
        />
      </LzDialog>
    </div>
  );
};
