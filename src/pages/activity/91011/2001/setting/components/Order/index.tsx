import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Grid, Input, NumberPicker, Radio, Icon, Balloon, Select } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, getShopOrderStartTime, isPopShop, validateActivityThreshold } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';
import ChooseGoods from '@/components/ChooseGoods';
import SkuList from '@/components/SkuList';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [
  { label: '已付款', value: 0 },
  { label: '已完成', value: 1 },
];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: 0,
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRestrainRangeData');
    }, 1000);
  }, [formData.endTime]);
  const [maxDays, setMaxDays] = React.useState(180);
  const [firstIn, setFirstIn] = React.useState(false);
  useEffect(() => {
    if (!firstIn) {
      setFirstIn(true);
      return;
    }
    let diff = 180;
    if (shopOrderInfo.longTermOrder) {
      diff = dayjs(formData.endTime).diff(dayjs(shopOrderInfo.shopOrderStartTime), 'day');
    } else {
      diff = shopOrderInfo.orderRetentionDays;
    }
    setMaxDays(diff);
    if (diff < formData.days) {
      setData({ days: diff });
    }
  }, [formData.startTime, shopOrderInfo?.shopOrderStartTime]);

  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    setData({
      orderRestrainRangeData,
      orderStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };
  const onHistoryDataRangeChange = (orderHistoryRangeData): void => {
    setData({
      orderHistoryRangeData,
      hisOrderStartTime: format.formatDateTimeDayjs(orderHistoryRangeData[0]),
      hisOrderEndTime: format.formatDateTimeDayjs(orderHistoryRangeData[1]),
    });
  };

  const handleSkuChange = (data) => {
    setData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    console.log('removeSku', index);
    formData.orderSkuList.splice(index, 1);
    setData({ orderSkuList: formData.orderSkuList });
  };

  const radioDisabled = () => {
    if (formData.orderRestrainStatus === 0) {
      return true;
    } else {
      return false;
    }
  };
  const handleStatusChange = (orderRestrainStatus) => {
    setData({ orderRestrainStatus });
    if (orderRestrainStatus === 0 && formData.isDelayedDisttribution === 1) {
      setData({ orderRestrainStatus: 0, isDelayedDisttribution: 0, awardDays: 0 });
    }
  };

  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    // const { orderStartTime, orderEndTime } = formData;
    const orderStartTime = val[0];
    const orderEndTime = val[1];
    if (!orderStartTime || !orderEndTime) {
      callback('请选下单时间');
    } else if (
      !shopOrderInfo.longTermOrder &&
      dayjs(orderStartTime).valueOf() < shopOrderInfo.shopOrderStartTime
    ) {
      callback(`下单时间不能早于活动结束时间前${shopOrderInfo.orderRetentionDays}天`);
    } else if (dayjs(orderEndTime).startOf('s').isAfter(dayjs(formData.endTime))) {
      callback('下单时间不能晚于活动结束时间');
    } else {
      callback();
    }
  };
  // 历史订单事件校验
  const validateHistoryOrderTime = (rule, val, callback): void => {
    // const { orderStartTime, orderEndTime } = formData;
    const hisOrderStartTime = val[0];
    const hisOrderEndTime = val[1];
    if (!hisOrderStartTime || !hisOrderEndTime) {
      callback('请选择历史订单时间');
    } else if (dayjs(hisOrderEndTime).startOf('s').isAfter(dayjs(formData.orderStartTime))) {
      callback('历史订单结束时间不能晚于下单开始时间');
    } else {
      callback();
    }
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  const handlePreview = (data) => {
    setData({ orderSkuListPreview: data });
  };

  const MoveTarget = <Icon type="help" id="top" style={{ marginRight: '10px' }} />;
  const rule = {
    1:
      '会员新单参与规则：\n' +
      '1，只统计【用户成为会员入会以后】的订单；\n' +
      '2，会员人群：默认选定下单之前的半年时间 - 没有购买记录的用户（具体未购时长配置可在【新单规则】手动配置）；',
    2:
      '店铺新单参与规则：\n' +
      '1，只统计【用户在店铺是否有过已完成的订单】，跟订单是否是会员身份购买无关；\n' +
      '2，新单人群：默认选定下单之前的半年时间 - 没有购买记录的用户（具体未购时长配置可在【新单规则】手动配置）',
  };
  const numbers =
    '1）如设置[单笔] (任意一笔订单) 则用户在满足订单规则前提下完成多笔订单，如任意一笔订单全额满足订单金额，则有领奖资格:\n' +
    '2）如设置[多笔]订单，则用户的[符合条件订单数] 满足最小订单条件，即可领奖';

  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          {!!formData.limitOrder && (
            <>
              <FormItem label="下单时间" required requiredMessage="请选择下单时间" validator={validateOrderTime}>
                <RangePicker
                  className="w-300"
                  name="orderRestrainRangeData"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  value={
                    formData.orderRestrainRangeData || [
                      new Date(formData.orderStartTime),
                      new Date(formData.orderEndTime),
                    ]
                  }
                  onChange={onDataRangeChange}
                  disabledDate={(date) => {
                    return date.startOf('day').valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).valueOf();
                  }}
                />
                <div className={styles.tip}>
                  注：1、默认支持查询
                  {shopOrderInfo.longTermOrder
                    ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                    : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
                  的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
                  <br />
                  2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。
                </div>
              </FormItem>
              <FormItem
                label="历史订单时间"
                required
                requiredMessage="请选择历史订单时间"
                validator={validateHistoryOrderTime}
              >
                <RangePicker
                  className="w-300"
                  name="orderHistoryRangeData"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  value={
                    formData.orderHistoryRangeData || [
                      new Date(formData.hisOrderStartTime),
                      new Date(formData.hisOrderEndTime),
                    ]
                  }
                  onChange={onHistoryDataRangeChange}
                  disabledDate={(date) => {
                    return date.startOf('day').valueOf() > dayjs(formData.orderStartTime).valueOf();
                  }}
                />
              </FormItem>

              <FormItem label="新老客" required>
                <FormItem>
                  <Radio checked value={1}>新客</Radio>
                  <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover">
                    <div>老客：下单开始时间之前入会并且在历史订单时间内在全店已购</div>
                    <div>新客：其余场景都是新客</div>
                  </Balloon>
                </FormItem>
              </FormItem>

              <FormItem
                label="订单状态"
                extra={
                  <div className="next-form-item-help">
                    {/* 已付款：用户付款后即可参与活动 */}
                    {/* <br /> */}
                    已完成：(1)用户订单完成后才可参与活动。(2)预售商品需要支付尾款方可参与活动
                  </div>
                }
              >
                <Select
                  dataSource={ORDER_STATUS}
                  value={formData.orderRestrainStatus}
                  onChange={handleStatusChange}
                  disabled
                />
                <div className={styles.orderTypes}>
                  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}>
                    已付款
                  </span>
                  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}>
                    待出库
                  </span>
                  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}>
                    待发货
                  </span>
                  <span className={styles.order}>已完成</span>
                </div>
              </FormItem>
              {/* <FormItem label="订单笔数" required>
                <FormItem>
                  <Radio.Group
                    value={formData.orderStrokeCount}
                    onChange={(orderStrokeCount) => setData({ orderStrokeCount })}
                  >
                    <Radio value={1}>单笔</Radio>
                    <Radio value={2}>多笔</Radio>
                    <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover">
                      {numbers}
                    </Balloon>
                  </Radio.Group>
                </FormItem>
                {formData.orderStrokeCount === 2 && (
                  <FormItem required requiredMessage="请输入订单笔数">
                    大于等于{' '}
                    <NumberPicker
                      name="orderStrokeStatus"
                      min={1}
                      max={5}
                      type="inline"
                      value={formData.orderStrokeStatus}
                      onChange={(orderStrokeStatus: number) => setData({ orderStrokeStatus })}
                    />{' '}
                    笔
                  </FormItem>
                )}
              </FormItem> */}
              {/* <FormItem label="奖品延迟发放" required> */}
              {/*  <FormItem> */}
              {/*    <Radio.Group */}
              {/*      value={formData.isDelayedDisttribution} */}
              {/*      onChange={(isDelayedDisttribution) => */}
              {/*        setData({ isDelayedDisttribution, awardDays: isDelayedDisttribution }) */}
              {/*      } */}
              {/*    > */}
              {/*      <Radio value={0}>否</Radio> */}
              {/*      <Radio value={1} disabled={radioDisabled()}> */}
              {/*        是 */}
              {/*      </Radio> */}
              {/*    </Radio.Group> */}
              {/*  </FormItem> */}
              {/*  {formData.isDelayedDisttribution === 1 && formData.orderRestrainStatus === 1 && ( */}
              {/*    <FormItem required requiredMessage="请输入延迟天数"> */}
              {/*      延迟发放{' '} */}
              {/*      <NumberPicker */}
              {/*        name="awardDays" */}
              {/*        min={1} */}
              {/*        max={99} */}
              {/*        type="inline" */}
              {/*        value={formData.awardDays} */}
              {/*        onChange={(awardDays: number) => setData({ awardDays })} */}
              {/*      />{' '} */}
              {/*      天 */}
              {/*    </FormItem> */}
              {/*  )} */}
              {/* </FormItem> */}
              <FormItem label="价格类型">
                <Radio.Group value={formData.priceType} onChange={(val) => setData({ priceType: val })}>
                  {/* <Radio value={0} disabled>
                    京东价
                  </Radio> */}
                  <Radio value={1}>
                    实付价
                  </Radio>
                </Radio.Group>
              </FormItem>
              {/* <FormItem
                label={formData.orderStrokeCount === 1 ? '任意笔订单金额' : '总订单金额'}
                required
                requiredMessage="请输入订单金额"
              >
                大于等于{' '}
                <NumberPicker
                  precision={2}
                  name="orderAmount"
                  min={0}
                  max={9999999}
                  type="inline"
                  value={formData.orderAmount}
                  onChange={(orderAmount: number) => setData({ orderAmount })}
                />{' '}
                元<p className={styles.tip}>注：若选择指定商品，则订单中指定商品金额需满足所设值</p>
              </FormItem> */}
              <FormItem label="订单商品" required>
                <RadioGroup
                  value={formData.orderSkuExposure}
                  onChange={(orderSkuExposure: number) => {
                    setData({ orderSkuExposure, orderSkuList: [], orderSkuListPreview: [] });
                  }}
                >
                  <Radio id="0" value={0}>
                    全部商品
                  </Radio>
                  <Radio id="1" value={1}>
                    指定商品
                  </Radio>
                  <Radio id="2" value={2}>
                    排除商品
                  </Radio>
                </RadioGroup>
                <Grid.Row>
                  {(formData.orderSkuExposure === 1 || formData.orderSkuExposure === 2) && (
                    <FormItem
                      name="orderSkuList"
                      required
                      requiredMessage={'请选择订单商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <Input
                        className="validateInput"
                        name="orderSkuList"
                        value={formData.orderSkuList.length ? 1 : ''}
                      />
                      <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} />
                      {activityEditDisabled() && (
                        <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} />
                      )}
                      {!activityEditDisabled() && (
                        <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={(skuId: any) => {
                          formData.orderSkuList = formData.orderSkuList.filter((item: any) => item.skuId !== skuId);
                          setData({ orderSkuList: formData.orderSkuList });
                        }} />
                      )}
                      <p className={styles.tip}>注：商品价格每天凌晨同步;</p>
                    </FormItem>
                  )}
                </Grid.Row>
              </FormItem>
            </>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
