/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  exchangeImg?: string;
  equityType?: number;
  orderAmount: string;
  sortId?: number;
}

export interface CustomValue {
  // 活动主图
  actBg: string;
  // 页面背景图
  pageBg: string;
  // 页面背景颜色
  actBgColor: string;
  // 文字颜色
  shopNameColor: string;
  // 活动规则
  ruleBg: string;
  // 我的奖品
  myPrizeBg: string;
  qy1Bg: string;
  qy2Bg: string;
  qy2BgItemBg: string;
  qy2BgItemBgArr: any[];
  qy3Bg: string;
  // 活动商品背景
  showSkuBg: string;
  hotZoneSetting: {
    bg: string;
    hotZoneList: [];
  };
  // 是否能给关闭入会弹窗
  canNotCloseJoinPopup: string;
  jumpUrl: string;
  isShowJump: boolean;
  moreActLink: string;
  // hotZoneList: any[];

  cmdImg: string;
  h5Img: string;
  mpImg: string;
}

export interface PageData {
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  partake: number;
  partakeRangeData: [string, string] | null;
  partakeStartTime: string;
  partakeEndTime: string;
  crowdPackage: number;
  limitOrder: number;
  firstBuyType: number;
  orderRestrainRangeData: [Dayjs, Dayjs];
  orderStartTime: string;
  orderEndTime: string;
  orderRestrainStatus: number;
  orderStrokeCount: number;
  awardDays: number;
  isDelayedDisttribution: number;
  orderStrokeStatus: number;
  orderAmount: string;
  isSkuExposure: number;
  orderSkuList: any[];
  crowdBag: any;
  totalProbability: number;
  couponPrizeList: [];
  prizeList: any[]; // 根据实际情况，可能需要定义奖品的类型
  receiveNum: number; // 最多能领几份奖品
  endActivity: number;
  isExposure: number;
  skuList: any[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  priceType: number;
  orderHistoryRangeData?: any[];
  hisOrderStartTime?: string;
  hisOrderEndTime?: string;
  prizeListOne: any[];
  prizeListTwo: any[];
  orderSkuExposure: number;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 活动规则
  ruleBg: '',
  // 我的奖品
  myPrizeBg: '',
  qy1Bg: '',
  qy2Bg: '',
  qy2BgItemBg: '',
  qy2BgItemBgArr: [],
  qy3Bg: '',
  // 参与活动商品背景
  showSkuBg: '',
  hotZoneSetting: {
    bg: '',
    hotZoneList: [],
  },
  // 是否能关闭入会弹窗
  canNotCloseJoinPopup: '1',
  jumpUrl: '',
  isShowJump: true,
  moreActLink: '',
  // hotZoneList: [],
  cmdImg: '',
  h5Img: '',
  mpImg: '',

};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `首购有礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 参与时段限制（不提交）
    partake: 0,
    // 参与时段限制（不提交）
    partakeRangeData: null,
    // 参与开始时间
    partakeStartTime: '',
    // 参与结束时间
    partakeEndTime: '',
    // 参与者生成人群包
    crowdPackage: 0,
    // 是否延迟发奖
    isDelayedDisttribution: 0,
    // 延迟延迟发奖天数
    awardDays: 0,
    // 限制订单
    limitOrder: 1,
    // 首购类型  0:会员首购 1:店铺首购
    firstBuyType: 1,
    // 限制订单时间
    orderRestrainRangeData: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 限制订单开始时间
    orderStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 限制订单结束时间
    orderEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 限制订单状态
    orderRestrainStatus: 1,
    // 限制1：单笔 2：多笔
    orderStrokeCount: 1,
    // 订单笔数
    orderStrokeStatus: 2,

    // 限制订单金额
    orderAmount: '0',
    // 订单商品全点商品或指定商品
    orderSkuExposure: 0,
    isSkuExposure: 0, // 是否设置曝光商品
    // 限制订单商品列表
    orderSkuList: [],
    // 中奖总概率
    totalProbability: 0,
    couponPrizeList: [],
    // 奖品列表
    prizeList: [],
    prizeListOne: [], // 权益1奖品
    prizeListTwo: [], // 权益2奖品

    // 最多能领几份奖品
    receiveNum: 1,
    // 全部奖品发放完强制结束活动
    endActivity: 0,
    // 是否开启曝光
    isExposure: 1,
    // 商品列表
    skuList: [],
    // 是否开启分享
    shareStatus: 0,
    // 分享标题
    shareTitle: '会员新单有礼',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    priceType: 1,
    orderHistoryRangeData: [],
    hisOrderStartTime: '',
    hisOrderEndTime: '',
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '谢谢参与',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  equityType: 1,
  orderAmount: '1',
  sortId: 1,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};
// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit' && getParams('status') === '2';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 订单时间超出活动时间范围内
const isOrderTimeInvalid = (formData: PageData) => {
  if (dayjs(formData.startTime).subtract(180, 'days').valueOf() > dayjs(formData.orderStartTime).valueOf()) {
    Message.error('订单开始时间不得超过活动开始时间前180天');
    return false;
  }
  if (formData.endTime < formData.orderEndTime) {
    Message.error('订单结束时间不得超过活动结束时间');
    return false;
  }
  return true;
};

// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  if (prize.equityType === 2) {
    if (!prize.orderAmount || Number(prize.orderAmount) <= 0) {
      Message.error('请设置领奖条件');
      return false;
    }
  }
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  // 红包不校验时间
  if (type === 0 || type === 3 || type === 4 || type === 6) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    const isStartCurrent: boolean = dayjs().isBefore(dayjs(start));
    if (isProcessingEditType()) {
      if (isStartCurrent) {
        Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于当前时间`);
        return false;
      }
    } else {
      if (isStart) {
        Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);
        return false;
      }
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const delayeTime = dayjs(formData.endTime).add(formData.awardDays, 'day');
    const isEnd: boolean = dayjs(delayeTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(
        `奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间${formData.awardDays ? `+延迟发奖天数` : ''
        }`,
      );
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (formData: PageData): boolean => {
  for (let i = 0; i < formData.prizeListOne.length; i++) {
    if (!isPrizeValid(formData.prizeListOne[i], formData)) {
      return false;
    }
  }
  return true;
};
const arePrizesValidTwo = (formData: PageData): boolean => {
  for (let i = 0; i < formData.prizeListTwo.length; i++) {
    if (!isPrizeValid(formData.prizeListTwo[i], formData)) {
      return false;
    }
  }
  return true;
};
const hasPrize = (prizeListOne: PrizeInfo[], prizeListTwo: PrizeInfo[], decoData): boolean => {
  // console.log(decoData, 'decoData========');
  const prizeListOne1 = prizeListOne.filter((e) => e.prizeType <= 0 || !e.prizeType);
  if (prizeListOne1.length > 0) {
    Message.error('请完善首购权益1奖品');
    return false;
  }
  const prizeListTwo1 = prizeListTwo.filter((e) => e.prizeType <= 0 || !e.prizeType);
  if (prizeListTwo1.length > 0) {
    Message.error('请完善首购权益2奖品');
    return false;
  }
  const decoData1 = JSON.parse(decoData);
  if (prizeListTwo.length !== decoData1.qy2BgItemBgArr.length) {
    Message.error('权益二设置的奖品图数量与奖品个数应相同，请检查');
    return false;
  }
  return true;
};

const checkSkuList = (isExposure, skuList) => {
  if (isExposure) {
    if (skuList.length === 0) {
      Message.error('请选择曝光商品');
      return false;
    }
  }
  return true;
};

export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData, decoData): boolean => {
  // 编辑的时候奖品和曝光商品需要校验
  // console.log(decoData, 'decoDataaaaaaaaaaaaa');
  if (isProcessingEditType()) {
    // 没有选择奖品
    if (!hasPrize(formData.prizeListOne, formData.prizeListTwo, decoData)) {
      return false;
    }
    // 奖品时间与活动时间冲突
    if (!arePrizesValid(formData)) {
      return false;
    }
    if (!arePrizesValidTwo(formData)) {
      return false;
    }
    // 检测曝光商品
    if (!checkSkuList(formData.isExposure, formData.skuList)) {
      return false;
    }
    formData.prizeList = formData.prizeListOne.concat(formData.prizeListTwo);
    return true;
  } else {
    // 没有选择奖品
    if (!hasPrize(formData.prizeListOne, formData.prizeListTwo, decoData)) {
      return false;
    }
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
    // 结束时间异常
    if (!isEndTimeValid(formData.startTime, formData.endTime)) {
      return false;
    }
    // 订单时间超出活动时间范围内
    if (!isOrderTimeInvalid(formData)) {
      return false;
    }
    // 奖品时间与活动时间冲突
    if (!arePrizesValid(formData)) {
      return false;
    }
    if (!arePrizesValidTwo(formData)) {
      return false;
    }

    // 检测曝光商品
    if (!checkSkuList(formData.isExposure, formData.skuList)) {
      return false;
    }
    formData.prizeList = formData.prizeListOne.concat(formData.prizeListTwo);
    return true;
  }
};
