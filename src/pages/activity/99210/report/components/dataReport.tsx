import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataDailyData, dataDailyDataExport } from '@/api/v99210';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import constant from '@/utils/constant';
import LzPanel from '@/components/LzPanel';

import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;

const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];
  const [packVisible, setPackVisible] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataDailyData(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataDailyDataExport(formValue).then((data: any) => downloadExcel(data, '活动数据'));
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <LzPanel>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="查询时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        {/* <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}> */}
        {/*  生成人群包 */}
        {/* </Button> */}
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="日期" dataIndex="dayTime" />
        <Table.Column title="PV" dataIndex="pv" />
        <Table.Column title="UV" dataIndex="uv" />
        <Table.Column title="入会人数" dataIndex="memberNum" />
        <Table.Column title="领取令牌数" dataIndex="receiveNum" />
        <Table.Column title="领取-付款人数" dataIndex="buyNum" />
        <Table.Column title="领取-付款金额" dataIndex="buyPrice" />
        <Table.Column title="UV-付款人数" dataIndex="uvBuyNum" />
        <Table.Column title="UV-付款金额" dataIndex="uvBuyPrice" />
        {/* <Table.Column title="客单价" dataIndex="customerPrice" /> */}
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </LzPanel>
  );
};
