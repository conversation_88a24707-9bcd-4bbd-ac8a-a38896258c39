/**
 * Author: zhangyue
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface CustomValue {
  pageBg: string;
  actBg: string;
  actBgColor: string;
  btnColor: string;
  btnBg: string;
  btnBorderColor: string;
  isShowSuspend: boolean;
  suspendImg: string;
  suspendLink: string;
  // step: string;
  skuModuleBg: string;
  skuItemBg: string;
  skuBtnColor: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  shopNameColor: string;
  disableShopName: number;
}

interface OrderData {
  assignGoodsFlag: string;
  orderCnt: number;
  orderCntFlag: number;
  orderRangeDate: any[];
  confirmRangeDate: any[];
  orderSkuList: any[];
  orderStatus: number;
  orderType: number;
  priceTypeFlag: string;
  totalOrderAmount: string;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  orderRangeDate: [Dayjs, Dayjs];
  confirmRangeDate: [Dayjs, Dayjs];
  giftRangeDate: [Dayjs, Dayjs];
  powerRangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  isExposure: number;
  shareStatus: number;
  shareTitle: string;
  mutualActivityIds: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  skuList: any;
  priceType: number;
  countOrderType: number;
  totalOrderAmount: string;
  orderDataList: OrderData[];
  points: number;
  prizeList: any;
  orderSkuList: any;
  overDays: number;
  crowdBag: any;
  autoAward: number;
}

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  btnColor: '', // 按钮字体颜色
  btnBg: '', // 按钮背景颜色
  btnBorderColor: '', // 按钮边框颜色
  isShowSuspend: false,
  suspendImg: '',
  suspendLink: '',
  // step: '',
  skuModuleBg: '',
  skuItemBg: '',
  skuBtnColor: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  shopNameColor: '', // 店铺名称颜色
  disableShopName: 0,
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `积分锁权-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 下单时间
    orderRangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 确认收货时间
    confirmRangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 领奖时间
    giftRangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 锁权时间
    powerRangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5,-9',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '下单即有机会赢取好礼，快来看看吧！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    // 是否添加曝光商品 0 否 1 是
    isExposure: 0,
    // 曝光商品列表
    skuList: [],
    // 价格类型
    priceType: 0,
    // 订单计算方式
    countOrderType: 0,
    // 活动隔离
    mutualActivityIds: '',
    // 总订单金额
    totalOrderAmount: '',
    // 订单规则
    orderDataList: [],
    points: 10,
    prizeList: [],
    orderSkuList: [],
    overDays: 7,
    crowdBag: null,
    autoAward: 2,
  };
};

// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};
// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));
  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 校验订单规则
const isOrderValid = (formatData: PageData): boolean => {
  if (!formatData.orderDataList.length) {
    Message.error('请选添加订单规则');
    return false;
  }
  if (dayjs(formatData.orderDataList[0].orderRangeDate[0]).isBefore(dayjs(formatData.startTime))) {
    Message.error('下单时间不能早于活动开始时间');
    return false;
  }
  if (
    dayjs(formatData.orderDataList[formatData.orderDataList.length - 1].orderRangeDate[1]).isAfter(
      dayjs(formatData.endTime),
    )
  ) {
    Message.error('下单时间不能晚于活动结束时间');
    return false;
  }

  if (dayjs(formatData.orderDataList[0].confirmRangeDate[0]).isBefore(dayjs(formatData.startTime))) {
    Message.error('收货时间不能早于活动开始时间');
    return false;
  }
  if (
    dayjs(formatData.orderDataList[formatData.orderDataList.length - 1].confirmRangeDate[1]).isAfter(
      dayjs(formatData.endTime),
    )
  ) {
    Message.error('收货时间不能晚于活动结束时间');
    return false;
  }
  if (formatData.countOrderType) {
    for (const i in formatData.orderDataList) {
      if (!formatData.orderDataList[i].totalOrderAmount) {
        Message.error('请补充订单规则中门槛金额');
        return false;
      }
    }
  }
  return true;
};

// eslint-disable-next-line complexity
export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (formData.mutualActivityIds.includes(',')) {
    const mutualList = formData.mutualActivityIds.split(',');
    mutualList.forEach((item) => item.length);
    for (const item of mutualList) {
      if (item.length > 20) {
        Message.error('请输入正确的活动ID');
        return false;
      }
    }
  } else if (formData.mutualActivityIds?.length > 20) {
    Message.error('请输入正确的活动ID');
    return false;
  }
  const regex = /^[\d,]+$/;
  if (formData.mutualActivityIds && !regex.test(formData.mutualActivityIds)) {
    Message.error('活动隔离只允许输入数字和英文逗号');
    return false;
  }
  if (formData.isExposure && !formData.skuList.length) {
    Message.error('请选择曝光商品');
    return false;
  }
  if (!isOrderValid(formData)) {
    return false;
  }
  if (!formData.prizeList.length) {
    Message.error('请选择实物');
    return false;
  }
  return true;
};
