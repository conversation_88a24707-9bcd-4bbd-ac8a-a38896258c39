/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Form, Table, Input, Button, Dialog } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData } from '../util';
import dayjs from 'dayjs';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
// import LzImg from '@/components/LzImg';
// import CONST from '@/utils/constant';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [goodDialog, setGoodDialog] = useState(false);
  const [currentGood, setCurrentGood] = useState<any>([]);
  const showGoods = (good): void => {
    console.log(good);
    setCurrentGood(good);
    setGoodDialog(true);
  };

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
          formData.endTime,
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="锁权时间">
          {dayjs(formData.powerRangeDate[0]).format('YYYY-MM-DD HH:mm:ss')}至
          {dayjs(formData.powerRangeDate[1]).format('YYYY-MM-DD HH:mm:ss')}
        </FormItem>
        <FormItem label="兑换时间">
          {dayjs(formData.giftRangeDate[0]).format('YYYY-MM-DD HH:mm:ss')}至
          {dayjs(formData.giftRangeDate[1]).format('YYYY-MM-DD HH:mm:ss')}
        </FormItem>
        <FormItem label="活动隔离">{formData.mutualActivityIds}</FormItem>
        <FormItem label="订单限制天数">{formData.overDays}天</FormItem>
        <FormItem label="是否添加曝光商品">
          {!formData.isExposure && '否'}
          {!!formData.isExposure && <SkuList skuList={formData.skuList} />}
          {/* {!!formData.isExposure && ( */}
          {/*  <Table.StickyLock fixedHeader maxBodyHeight={550} dataSource={formData.skuList}> */}
          {/*    <Table.Column */}
          {/*      title="商品信息" */}
          {/*      width={260} */}
          {/*      cell={(val, index, record) => ( */}
          {/*        <div className={styles.part1} style={{ alignItems: 'center' }}> */}
          {/*          {record.skuMainPicture?.indexOf('360buyimg.com') > -1 ? ( */}
          {/*            <LzImg style={{ width: 60, height: 60, marginRight: '5px' }} src={record.skuMainPicture} /> */}
          {/*          ) : ( */}
          {/*            <LzImg */}
          {/*              style={{ width: 60, height: 60, marginRight: '5px' }} */}
          {/*              src={`${CONST.IMAGE_PREFIX}${record.skuMainPicture}`} */}
          {/*            /> */}
          {/*          )} */}
          {/*          <div> */}
          {/*            <div className={styles.part1_p1}>{record.skuName}</div> */}
          {/*            <div className={styles.part1_p2}>SKU编码：{record.skuId}</div> */}
          {/*          </div> */}
          {/*        </div> */}
          {/*      )} */}
          {/*    /> */}
          {/*    <Table.Column */}
          {/*      title="京东价(元)" */}
          {/*      cell={(val, index, info) => <span>{new Intl.NumberFormat().format(info.jdPrice)}</span>} */}
          {/*    /> */}
          {/*  </Table.StickyLock> */}
          {/* )} */}
        </FormItem>

        <FormItem label="价格类型">{formData.priceType === 0 ? '京东价' : '实付价'}</FormItem>

        <FormItem label="订单计算方式">
          {formData.countOrderType ? '按阶段金额计算' : `按总金额计算，大于等于${formData.totalOrderAmount}元`}
        </FormItem>

        <Form.Item label="订单规则">
          <Table dataSource={formData.orderDataList}>
            <Table.Column title="下单时间" dataIndex="orderRangeDate" cell={(val, index, row) => val.join(' ~ ')} />
            <Table.Column title="收货时间" dataIndex="confirmRangeDate" cell={(val, index, row) => val.join(' ~ ')} />
            {formData.countOrderType && (
              <Table.Column
                title="订单笔数"
                dataIndex="orderCntFlag"
                cell={(val, index, row) => (val ? `${row.orderCnt}笔` : '单笔')}
              />
            )}
            <Table.Column
              title="订单状态"
              dataIndex="orderStatus"
              cell={(val, index, row) => (val ? '已付款' : '已完成')}
            />
            {formData.countOrderType && <Table.Column title="总订单门槛" dataIndex="totalOrderAmount" />}
            <Table.Column
              title="订单商品"
              dataIndex="assignGoodsFlag"
              cell={(val, index, row) => (
                <div>
                  {row.assignGoodsFlag === 1 && (
                    <Button text type="primary" onClick={() => showGoods(row.orderSkuList)}>
                      指定商品
                    </Button>
                  )}
                  {row.assignGoodsFlag === 2 && (
                    <Button text type="primary" onClick={() => showGoods(row.orderSkuList)}>
                      排除商品
                    </Button>
                  )}
                  {row.assignGoodsFlag === 0 && <div> 全部商品</div>}
                </div>
              )}
            />
          </Table>
        </Form.Item>
        <FormItem label="达成获奖条件是否自动发奖">{formData.autoAward === 1 ? '是' : '否'}</FormItem>
        <FormItem label="兑换奖品所需积分">{formData.points}</FormItem>
        <FormItem label="实物">
          <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放总数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>

      <Dialog
        width={822}
        v2
        title="查看商品"
        visible={goodDialog}
        footer={false}
        onClose={() => setGoodDialog(false)}
        onOk={() => setGoodDialog(false)}
      >
        <div className={styles.showGoods}>
          <SkuList skuList={currentGood} />
          {/* {currentGood?.map((sku) => { */}
          {/*  return ( */}
          {/*    <div className={styles.skuContainer}> */}
          {/*      <img className={styles.skuImg} src={sku.skuMainPicture} alt="" /> */}
          {/*      <div> */}
          {/*        <div className={styles.skuName}>{sku.skuName}</div> */}
          {/*        <div className={styles.skuId}>SKUID:{sku.skuId}</div> */}
          {/*        <div className={styles.price}>¥ {sku.jdPrice}</div> */}
          {/*      </div> */}
          {/*    </div> */}
          {/*  ); */}
          {/* })} */}
        </div>
      </Dialog>
    </div>
  );
};
