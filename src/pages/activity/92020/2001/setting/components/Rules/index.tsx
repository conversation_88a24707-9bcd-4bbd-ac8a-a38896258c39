/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Button, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { checkActivityData, FormLayout, PageData, PrizeInfo } from '../../../util';
import format from '@/utils/format';
import { getShop } from '@/utils/shopUtil';
import { isPopShop } from '@/utils';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  checkForm: () => boolean;
}

export default ({ onChange, defaultValue, value, sRef, checkForm }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  const generateOrderString = () => {
    const orderDataList = formData.orderDataList.map((item, index) => {
      return `（${index + 1}）下单时间${format.formatDateTimeDayjs(
        item.orderRangeDate[0],
      )}至${format.formatDateTimeDayjs(item.orderRangeDate[1])}，收货时间${format.formatDateTimeDayjs(
        item.confirmRangeDate[0],
      )}至${format.formatDateTimeDayjs(item.confirmRangeDate[1])}，${
        formData.countOrderType
          ? `${item.orderCntFlag ? `大于等于${item.orderCnt}笔` : '单笔'}订单累计${isPopShop() ? '实付' : ''}金额达到${
              item.totalOrderAmount
            }元，`
          : ''
      }参与商品为${item.assignGoodsFlag === 1 || item.assignGoodsFlag === 2 ? '指定商品' : '全部商品'}；`;
    });
    return orderDataList.join('\n');
  };

  const generatePrizeString = () => {
    const prizeList = formData.prizeList.filter(
      (e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与',
    );
    const strList = prizeList.map((item, index): string => {
      return `${item.prizeName}: 限量${item.sendTotalCount}份，单份奖品价值${Number(item.unitPrice).toFixed(2)}元`;
    });
    return strList.join('\n');
  };

  /**
   * 自动生成规则说明
   */
  const autoCreateRuleDesc = async (): Promise<void> => {
    if (!checkForm()) return;
    const isValidateData: boolean = checkActivityData(formData);
    if (!isValidateData) {
      return;
    }
    const rules = `【活动时间】（以北京时间为准）：
·锁权时间：${format.formatDateTimeDayjs(formData.powerRangeDate[0])}至${format.formatDateTimeDayjs(
      formData.powerRangeDate[1],
    )}
·兑礼时间：${format.formatDateTimeDayjs(formData.giftRangeDate[0])}至${format.formatDateTimeDayjs(
      formData.giftRangeDate[1],
    )}

【下单规则】
${
  !formData.countOrderType
    ? `付款总金额大于${formData.totalOrderAmount}元；
`
    : ''
}${generateOrderString()}

【活动奖品】
活动奖品：${generatePrizeString()}
*活动期间，积分锁权好礼同一用户限量1份，先到先得，赠完即止。

【活动参与方式】
1.会员在锁定权益阶段于指定页面消耗${formData.points}积分完成锁权，并预留收货地址。
2.会员锁定权益后进入下单购买阶段，该阶段会员需在指定期限内于京东【${getShop().shopName}】购买正装产品累计${
      isPopShop() ? '实付' : ''
    }金额达到上述规则（专享价/入会礼等试用装不计入正装产品，${
      isPopShop() ? '实付' : ''
    }金额中不包含售中退款/售后退款、红包、优惠券、购物金、品牌储值卡、膨胀金、京东E卡），且在确认收货后7天内未进行退货退款导致${
      isPopShop() ? '实付' : ''
    }金额不满足活动门槛，即可等待发放奖品，奖品数量有限，先到先得，赠完即止。

【如何锁权】
1.如未锁定权益，会员需登录京东APP/网页，搜索【${
      getShop().shopName
    }】，进入活动页，点击【点击锁权】按钮，在锁权提示弹窗点击【确认锁权】后消耗${formData.points}积分，即锁权成功。
2.如已锁定权益，可在【锁权记录】中查看。

【如何获奖】
1.锁权成功将会提示预留收货信息，信息一经提交无法修改，订单满足活动要求的用户，奖品将于活动结束后30个工作日内，按照预留的地址发出（如遇疫情等不可抗因素，将相应延迟发放）。如果没有提前预留地址，可在活动主页【锁权记录-收货信息】位置填写收货地址，且必须于填写收货信息截止时间前完成填写，逾期视为放弃领奖资格。
2.用户需对活动期间的订单确认收货满7天，且需持续持有京东【${getShop().shopName}】的会员身份，若中途退出【${
      getShop().shopName
    }】会员，则失去获奖资格，再次入会也无法恢复资格。
3.如成功参与活动的用户对商品订单进行退款处理，导致实际支付金额不足活动门槛金额的，视为用户不符合兑换好礼资格，商家有权取消该用户获取奖品的资格。同一用户多个订单满足活动要求，也仅可获得1份奖品。同一用户定义详情见【注意事项】。
4.由于活动奖品数量有限，系统将按照订单满足活动要求、且已提交收货信息的用户进行发奖。如果最后一份奖品排序时间出现同一时间点内有多个用户满足发奖要求，则会采取系统随机的方式进行抽取。
5.奖品发放形式：
【实物奖】：
（1）用户中奖后，会通过弹窗的形式收集中奖用户的姓名、联系电话、详细地址，实物奖品采取寄送方式发放；
（2）如出现下列情况，实物奖品将不予发放：① 因用户原因无法取得联系；② 用户未填写真实有效的信息或填写收货信息不详；③ 存在多个用户填写相同中奖人信息，则系统判自动判定为恶意刷奖行为；④超过1小时未填写地址视为放弃；
【虚拟奖品】：
（1）红包、优惠券、京豆、积分、E卡，预计会在24小时内发放到京东账户中，有效期、商品范围、使用规则等以官方券面展示信息为主准，您可以在“京东APP-我的”中查看；
（2）爱奇艺会员卡、PLUS会员卡需手动兑换；

【注意事项】
1.同一用户仅限参与活动一次。同一用户是指账户、手机号码、收件地址、支付账号、身份证号码、设备标识符、注册信息、网络操作日志、交易订单信息等与用户身份和行为相关的信息，其中任意一项或数项存在相同、相似，亦或通过特定标记批量信息进行分析，其数项存在相同、相似的（包括但不限于批量相同、雷同、邻近、虚构的地址）均视为同一用户。同一用户注册、持有、使用或控制多个会员账号，商家有权取消/限制其参与本活动的资格。
2.活动期间，若发现用户使用或曾使用不正当方式（包括但不限于批量注册，恶意套现，虚假分享，虚假交易，网络攻击，众包作弊，通过任何外挂软件破坏、规避活动规则）或仅以套取活动利益为目的参与本次活动，不符合活动公平、诚信原则的，商家有权直接取消或限制用户参与活动的资格；若用户已领取奖品奖励，商家有权追回（包括但不限于取消违反规则而形成的订单、奖励）；出现前述情形之一的，商家有权将视情况终止、限制用户后续参与商家任意活动的资格，并保留追究其法律责任的权利。
3.活动期间，如出现不可抗力或形势变更的情况，包括但不限于重大灾害事件、黑客攻击、系统故障、活动受政府机关指令需停止举办或调整本活动的，商家可依据相关法律法规的规定调整或暂停或提前终止活动。
4.本活动所发放的所有奖品不可折现. 不可转赠他人. 不可为他人付款，不支持开具发票。
5.实物奖品一经发出，无质量问题恕不退换，如有疑问，请咨询店铺客服。
6.商家可根据活动的实际进行情况，在法律允许的范围内，对本活动规则进行调整或变动，相关调整或变动将公布在活动规则页上。
7.如对本活动有任何疑问，请咨询在线客服。
`;
    setData({ rules });
    field.setErrors({ rules: '' });
  };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请生成/输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <Button
              type="primary"
              className="table-cell-btn"
              onClick={autoCreateRuleDesc}
              style={{ marginRight: '15px' }}
              text
            >
              自动生成规则说明
            </Button>
            <span style={{ color: 'red', fontSize: '12px' }}>
              提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。
            </span>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
