/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer } from 'react';
import { Form, Field, DatePicker2, Radio, Grid, NumberPicker, Input } from '@alifd/next';
import { FormLayout } from '../../../util';
import { activityEditDisabled } from '@/utils';
import constant from '@/utils/constant';
import dayjs from 'dayjs';
import styles from '../../style.module.scss';
import ChooseGoods from '@/components/ChooseGoods';
import LzTip from '@/components/LzTip';
import format from '@/utils/format';

const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker2;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { Row, Col } = Grid;

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  orderDataList: any;
  onChange: (formData: any) => void;
  onCancel: () => void;
  countOrderType: number;
}

const defaultValue = {
  assignGoodsFlag: 0,
  orderCnt: 1,
  orderCntFlag: 0,
  orderRangeDate: [],
  confirmRangeDate: [],
  orderSkuList: [],
  orderStatus: 0,
  orderType: 0,
  priceTypeFlag: '',
  totalOrderAmount: '',
};

export default ({ onChange, onCancel, orderDataList, countOrderType }: Props) => {
  const field: Field = Field.useField({ values: orderDataList || defaultValue });
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, orderDataList || defaultValue);

  const handleSkuChange = (data) => {
    setFormData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };

  const onOrderRangeChange = (orderRangeDate): void => {
    setFormData({
      orderRangeDate: [format.formatDateTimeDayjs(orderRangeDate[0]), format.formatDateTimeDayjs(orderRangeDate[1])],
    });
  };

  const onConfirmRangeDate = (confirmRangeDate): void => {
    setFormData({
      confirmRangeDate: [
        format.formatDateTimeDayjs(confirmRangeDate[0]),
        format.formatDateTimeDayjs(confirmRangeDate[1]),
      ],
    });
  };

  const submit = () => {
    field.validate((err) => {
      if (err) {
        return;
      }
      onChange(formData);
    });
  };

  const checkOrderRangeDate = (rule: any, value: any, callback: any) => {
    if (!value[0] || !value[1]) {
      callback('请选择下单时间');
      return;
    }
    callback('');
  };

  const checkOrderRangeChange = (rule: any, value: any, callback: any) => {
    if (!value[0] || !value[1]) {
      callback('请选择收货时间');
      return;
    }
    callback('');
  };

  return (
    <div>
      <Form {...formItemLayout} disabled={activityEditDisabled()} field={field}>
        <Form.Item label="下单时间" required requiredMessage="请选择下单时间" validator={checkOrderRangeDate}>
          <RangePicker
            className="w-300"
            name="orderRangeDate"
            inputReadOnly
            format={dateFormat}
            hasClear={false}
            showTime
            value={formData.orderRangeDate}
            onChange={onOrderRangeChange}
            disabledDate={(date) => {
              return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
            }}
          />
        </Form.Item>

        <Form.Item label="收货时间" required requiredMessage="请选择收货时间" validator={checkOrderRangeChange}>
          <RangePicker
            className="w-300"
            name="confirmRangeDate"
            inputReadOnly
            format={dateFormat}
            hasClear={false}
            showTime
            value={formData.confirmRangeDate}
            onChange={onConfirmRangeDate}
            disabledDate={(date) => {
              return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
            }}
          />
        </Form.Item>
        {!!countOrderType && (
          <>
            <Form.Item label="订单笔数" required>
              <RadioGroup
                value={formData.orderCntFlag}
                onChange={(orderCntFlag: number) => setFormData({ orderCntFlag })}
              >
                <Radio id="0" value={0}>
                  单笔
                </Radio>
                <Radio id="1" value={1}>
                  多笔
                </Radio>
              </RadioGroup>
            </Form.Item>
            {!!formData.orderCntFlag && (
              <Form.Item label=" " colon={false}>
                <Row gutter="4">
                  <Col>
                    {formData.orderCntFlag === 1 && (
                      <div className={styles.panel}>
                        <Form.Item
                          name="orderCnt"
                          required
                          requiredTrigger="onBlur"
                          requiredMessage="订单笔数"
                          style={{ margin: 0 }}
                        >
                          大于等于
                          <NumberPicker
                            value={formData.orderCnt}
                            onChange={(orderCnt: number) => setFormData({ orderCnt })}
                            type="inline"
                            min={1}
                            max={9999999}
                            className={styles.number}
                          />
                          笔
                        </Form.Item>
                      </div>
                    )}
                  </Col>
                </Row>
              </Form.Item>
            )}
          </>
        )}
        <Form.Item label="订单状态" required>
          <RadioGroup value={formData.orderStatus} onChange={(orderStatus: number) => setFormData({ orderStatus })}>
            <Radio id="0" value={0}>
              已完成
            </Radio>
            {/* <Radio id="1" value={1}> */}
            {/*   已付款 */}
            {/* </Radio> */}
          </RadioGroup>
        </Form.Item>
        {!!countOrderType && (
          <Form.Item required requiredTrigger="onBlur" label="总订单金额" requiredMessage="请输入总订单金额">
            大于等于
            <NumberPicker
              name="totalOrderAmount"
              value={formData.totalOrderAmount}
              onChange={(totalOrderAmount: number) => setFormData({ totalOrderAmount })}
              type="inline"
              min={1}
              max={9999999}
              precision={2}
              className={styles.number}
            />
            元
          </Form.Item>
        )}
        {/* <Form.Item */}
        {/*   name="overDays" */}
        {/*   required */}
        {/*   requiredTrigger="onBlur" */}
        {/*   label="订单限制天数" */}
        {/*   requiredMessage="请输入订单限制天数" */}
        {/* > */}
        {/*   <NumberPicker */}
        {/*     value={formData.overDays} */}
        {/*     onChange={(overDays: number) => setFormData({ overDays })} */}
        {/*     type="inline" */}
        {/*     min={1} */}
        {/*     max={9999999} */}
        {/*     className={styles.number} */}
        {/*   /> */}
        {/* </Form.Item> */}
        <Form.Item label="订单商品" required>
          <Form.Item>
            <Radio.Group
              value={formData.assignGoodsFlag}
              onChange={(assignGoodsFlag: number) => setFormData({ assignGoodsFlag, orderSkuList: [] })}
            >
              <Radio id="0" value={0}>
                全部商品
              </Radio>
              <Radio id="1" value={1}>
                指定商品
              </Radio>
              {/* <Radio id="2" value={2}> */}
              {/*  排除商品 */}
              {/* </Radio> */}
            </Radio.Group>
          </Form.Item>
          {formData.assignGoodsFlag === 1 && (
            <Form.Item required requiredMessage="请选择订单商品" validatorTrigger="onChange">
              <div>
                <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} />
                <LzTip>
                  选择指定商品后，活动也将展示指定商品，不展示曝光商品，不要填写影子分身SKU，否则会造成用户无法参加活动
                </LzTip>
              </div>
              <Input
                className="validateInput"
                style={{ display: 'block' }}
                name="orderSkuList"
                value={formData.orderSkuList}
              />
            </Form.Item>
          )}
          {formData.assignGoodsFlag === 2 && (
            <Form.Item required requiredMessage="请选择排除商品" validatorTrigger="onChange">
              <div>
                <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} />
                <LzTip>
                  选择排除商品后，活动也将展示从全店商品去掉排除商品后的商品，不展示曝光商品，不要填写影子分身SKU，否则会造成用户无法参加活动
                </LzTip>
              </div>
              <Input
                className="validateInput"
                style={{ display: 'block' }}
                name="orderSkuList"
                value={formData.orderSkuList}
              />
            </Form.Item>
          )}
        </Form.Item>
        {/*     存在未测试bug 暂时下线其他选项 */}
        {/* <Form.Item label="订单类型" required> */}
        {/*  <RadioGroup value={formData.orderType} onChange={(orderType: number) => setFormData({ orderType })}> */}
        {/*     <Radio id="1" value={1}> */}
        {/*      只现货 */}
        {/*     </Radio> */}
        {/*     <Radio id="2" value={2}> */}
        {/*      只预售 */}
        {/*     </Radio> */}
        {/*    <Radio id="0" value={0}> */}
        {/*      全部 */}
        {/*    </Radio> */}
        {/*  </RadioGroup> */}
        {/* </Form.Item> */}
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit
                className="form-btn"
                validate
                type="primary"
                onClick={submit}
                style={{ marginRight: '10px' }}
              >
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
    </div>
  );
};
