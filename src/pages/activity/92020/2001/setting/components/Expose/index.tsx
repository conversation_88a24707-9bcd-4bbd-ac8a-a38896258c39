/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 14:58
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import { Form, Field, Radio, Input, Grid, Loading } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { activityEditDisabled } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import ChooseGoods from '@/components/ChooseGoods';
import styles from './index.module.scss';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const handleSkuChange = (data) => {
    setData({ skuList: data });
    field.setErrors({ skuList: '' });
  };
  const removeSkus = (skuId: number) => {
    const delIndex = formData.skuList.findIndex((item: any) => item.skuId === skuId);
    formData.skuList.splice(delIndex, 1);
    setData({ skuList: formData.skuList });
    setTimeout(() => {
      setLoading(false);
    }, 500);
  };

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const handlePreview = (data) => {
    setData({ skuListPreview: data });
  };
  return (
    <div>
      <LzPanel title="曝光商品">
        <Form {...formItemLayout} field={field}>
          <FormItem label="是否添加曝光商品" required>
            <RadioGroup
              value={formData.isExposure}
              onChange={(isExposure: number) => {
                if (!isExposure) {
                  formData.skuList = [];
                  formData.skuListPreview = [];
                }
                setData({ isExposure, skuList: formData.skuList });
              }}
            >
              <Radio id="1" value={1}>
                是
              </Radio>
              <Radio id="0" value={0}>
                否
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {formData.isExposure === 1 && (
                <FormItem name="skuList" required requiredMessage={'请选择曝光商品'} style={{ marginTop: '15px' }}>
                  <ChooseGoods value={formData.skuList} onChange={handleSkuChange} max={300} />
                  {formData.skuList.length !== 0 && (
                    <Loading visible={loading}>
                      <SkuList
                        skuList={formData.skuList}
                        handlePreview={handlePreview}
                        removeSku={(skuId) => {
                          setLoading(true);
                          removeSkus(skuId);
                        }}
                      />
                    </Loading>
                  )}
                  <p className={styles.tip}>注：商品价格每天凌晨同步;</p>
                  <Input className="validateInput" name="skuList" value={formData.skuList} />
                </FormItem>
              )}
            </Grid.Row>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
