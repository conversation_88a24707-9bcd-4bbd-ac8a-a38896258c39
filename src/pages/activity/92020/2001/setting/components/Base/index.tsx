/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Input, DatePicker2, Field, NumberPicker } from '@alifd/next';
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import LzThreshold from '@/components/LzThreshold';
import styles from '../../style.module.scss';

const FormItem = Form.Item;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setData({
      rangeDate,
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };
  const onPowerRangeChange = (powerRangeDate): void => {
    setData({
      powerRangeDate,
    });
  };
  const ongiftRangeDate = (giftRangeDate): void => {
    setData({
      giftRangeDate,
    });
  };

  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="活动基本信息">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="活动名称" required requiredMessage="请输入活动名称" disabled={false}>
            <Input
              value={formData.activityName}
              placeholder="请填写活动名称"
              name="activityName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(activityName) => setData({ activityName })}
            />
          </FormItem>
          <FormItem
            label="活动时间"
            required
            requiredMessage="请选择活动时间"
            extra={<div className="next-form-item-help">注：活动时间需设置在软件的订购有效期内</div>}
          >
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <DateRangeDuration
              rangeDate={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
            />
          </FormItem>
          <FormItem label="活动门槛" required>
            <LzThreshold formData={formData} field={field} setData={setData} />
          </FormItem>

          <FormItem label="锁权时间" required requiredMessage="请选择锁权时间">
            <RangePicker
              className="w-300"
              name="powerRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.powerRangeDate}
              onChange={onPowerRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
          </FormItem>
          <FormItem label="兑换时间" required requiredMessage="请选择兑换时间">
            <RangePicker
              className="w-300"
              name="giftRangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.giftRangeDate}
              onChange={ongiftRangeDate}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
          </FormItem>
          <FormItem label="活动隔离">
            <Input
              value={formData.mutualActivityIds}
              placeholder="请填写活动ID,多个用逗号隔开"
              name="mutualActivityIds"
              showLimitHint
              maxLength={255}
              style={{ width: '500px' }}
              onChange={(mutualActivityIds) => setData({ mutualActivityIds })}
            />
            <div className={styles.tip}>
              注：输入需要隔离的活动，多个需用逗号隔开，设置成功后，若用户在任意一个隔离活动中进行锁权且锁权成功，则无法参与该锁权活动
            </div>
          </FormItem>
          <Form.Item
            name="overDays"
            required
            requiredTrigger="onBlur"
            label="订单限制天数"
            requiredMessage="请输入订单限制天数"
          >
            <NumberPicker
              value={formData.overDays}
              onChange={(overDays: number) => setData({ overDays })}
              type="inline"
              min={0}
              max={15}
              className={styles.number}
            />
            <div className={styles.tip}>
              限制商品确认收货后{formData.overDays}
              天未退款才可参与兑换，且活动时间要包含锁权时间、下单时间+（订单限制天数）、兑换时间。
            </div>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
