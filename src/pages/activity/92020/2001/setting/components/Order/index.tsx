/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import { Form, Field, Table, Button, Dialog, Message, Radio, NumberPicker } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { PageData, FormLayout } from '../../../util';
import { activityEditDisabled } from '@/utils';
import OrderRule from '../OrderRule';
import styles from '../../style.module.scss';
import dayJs from 'dayjs';
import SkuList from '@/components/SkuList';

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [show, setShow] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);

  const [goodDialog, setGoodDialog] = useState(false);
  const [currentGood, setCurrentGood] = useState<any>([]);
  const showGoods = (good): void => {
    console.log(good);
    setCurrentGood(good);
    setGoodDialog(true);
  };

  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data }, 'task');
  };

  const changeOrder = (orderDataList) => {
    if (target !== 0) {
      if (
        dayJs(orderDataList.orderRangeDate[0]).isBefore(dayJs(formData.orderDataList[target - 1].orderRangeDate[1]))
      ) {
        Message.error('下单时间不能早于上一条规则的结束时间');
        return;
      }
    }
    formData.orderDataList[target] = orderDataList;
    formData.orderDataList[target].orderSkuListPreview = orderDataList.orderSkuList;
    setData(formData);
    setShow(false);
  };

  /**
   * 添加任务组件成功回调
   * 向任务列表中添加数据 & 同步数据
   * @param taskInfo 任务信息
   */
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="订单规则">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item label="价格类型" required>
            <Radio.Group value={formData.priceType} onChange={(priceType: number) => setData({ priceType })}>
              <Radio id="0" value={0}>
                京东价
              </Radio>
              <Radio id="1" value={1}>
                实付价
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="订单计算方式" required>
            <Form.Item>
              <Radio.Group
                value={formData.countOrderType}
                onChange={(countOrderType: number) => setData({ countOrderType })}
              >
                <Radio id="0" value={0}>
                  按总金额计算
                </Radio>
                <Radio id="1" value={1}>
                  按阶段金额计算
                </Radio>
              </Radio.Group>
            </Form.Item>
            {!formData.countOrderType && (
              <Form.Item required requiredTrigger="onBlur" requiredMessage="请输入总订单金额" style={{ margin: 0 }}>
                大于等于
                <NumberPicker
                  name="totalOrderAmount"
                  value={formData.totalOrderAmount}
                  onChange={(totalOrderAmount: number) => setData({ totalOrderAmount })}
                  type="inline"
                  min={1}
                  max={9999999}
                  precision={2}
                  className={styles.number}
                />
                元
              </Form.Item>
            )}
          </Form.Item>
          <Form.Item label="订单规则" required>
            <Form.Item>
              <Button
                type="primary"
                disabled={formData.orderDataList.length >= 3}
                onClick={() => {
                  setEditValue(null);
                  setTarget(formData.orderDataList.length);
                  setShow(true);
                }}
              >
                添加规则
              </Button>
            </Form.Item>
            <Form.Item>
              <Table dataSource={formData.orderDataList} >
                <Table.Column title="下单时间" dataIndex="orderRangeDate" cell={(val, index, row) => val.join(' ~ ')} />
                <Table.Column
                  title="收货时间"
                  dataIndex="confirmRangeDate"
                  cell={(val, index, row) => val.join(' ~ ')}
                />
                {formData.countOrderType && (
                  <Table.Column
                    title="订单笔数"
                    dataIndex="orderCntFlag"
                    cell={(val, index, row) => (val ? `${row.orderCnt}笔` : '单笔')}
                  />
                )}
                <Table.Column
                  title="订单状态"
                  dataIndex="orderStatus"
                  cell={(val, index, row) => (val ? '已付款' : '已完成')}
                />
                {formData.countOrderType && <Table.Column title="总订单门槛" dataIndex="totalOrderAmount" />}
                <Table.Column
                  title="订单商品"
                  dataIndex="assignGoodsFlag"
                  cell={(val, index, row) => (
                    <div>
                      {row.assignGoodsFlag === 1 && (
                        <Button text type="primary" onClick={() => showGoods(row.orderSkuList)}>
                          指定商品
                        </Button>
                      )}
                      {row.assignGoodsFlag === 2 && (
                        <Button text type="primary" onClick={() => showGoods(row.orderSkuList)}>
                          排除商品
                        </Button>
                      )}
                      {row.assignGoodsFlag === 0 && <div> 全部商品</div>}
                    </div>
                  )}
                />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    cell={(val, index, row) => (
                      <div>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            setEditValue(row);
                            setTarget(index);
                            setShow(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该订单规则？',
                              onOk: () => {
                                formData.orderDataList.splice(index, 1);
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      </div>
                    )}
                  />
                )}
              </Table>
            </Form.Item>
          </Form.Item>
        </Form>

        <Dialog
          title="订单规则"
          visible={show}
          footer={false}
          onClose={() => setShow(false)}
          style={{ width: '670px' }}
        >
          <OrderRule
            orderDataList={editValue}
            onCancel={() => setShow(false)}
            onChange={changeOrder}
            countOrderType={formData.countOrderType}
          />
        </Dialog>

        <Dialog
          width={822}
          v2
          title="查看商品"
          visible={goodDialog}
          footer={false}
          onClose={() => setGoodDialog(false)}
          onOk={() => setGoodDialog(false)}
        >
          <div className={styles.showGoods}>
            <SkuList skuList={currentGood} />
            {/* {currentGood?.map((sku) => { */}
            {/*  return ( */}
            {/*    <div className={styles.skuContainer}> */}
            {/*      <img className={styles.skuImg} src={sku.skuMainPicture} alt="" /> */}
            {/*      <div> */}
            {/*        <div className={styles.skuName}>{sku.skuName}</div> */}
            {/*        <div className={styles.skuId}>SKUID:{sku.skuId}</div> */}
            {/*        <div className={styles.price}>¥ {sku.jdPrice}</div> */}
            {/*      </div> */}
            {/*    </div> */}
            {/*  ); */}
            {/* })} */}
          </div>
        </Dialog>
      </LzPanel>
    </div>
  );
};
