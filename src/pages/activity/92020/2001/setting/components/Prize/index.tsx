/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 14:58
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import { Form, Field, NumberPicker, Button, Table, Dialog, Radio } from '@alifd/next';
import styles from '../../style.module.scss';
import LzPanel from '@/components/LzPanel';
import { activityEditDisabled } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import JdProduct from '@/components/ChoosePrize/JdProduct';
import LzDialog from '@/components/LzDialog';

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();
  const [show, setShow] = useState(false);
  const [editValue, setEditValue] = useState<any>();

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const onSubmit = (prizeInfo) => {
    setShow(false);
    prizeInfo.prizeType = 3;
    setData({ prizeList: [prizeInfo] });
  };
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="奖项限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="达成获奖条件是否自动发奖" required>
            <RadioGroup value={formData.autoAward} onChange={(autoAward: number) => setData({ autoAward })}>
              <Radio id="1" value={1}>
                是
              </Radio>
              <Radio id="2" value={2}>
                否
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="兑换奖品所需积分" required requiredMessage="请输入兑换奖品所需积分" name="points">
            <NumberPicker
              value={formData.points}
              onChange={(points: number) => setData({ points })}
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
            />
            积分
          </FormItem>
          <FormItem label="选择实物" required>
            {formData.prizeList.length > 0 && (
              <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
                <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
                <Table.Column title="奖项名称" dataIndex="prizeName" />
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    if (row.prizeType === 1) {
                      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                    } else {
                      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                    }
                  }}
                />
                <Table.Column
                  title="发放总数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="单份价值(元)"
                  cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
                />
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <div>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            const row = formData.prizeList[index];
                            setEditValue(row);
                            setShow(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该奖品？',
                              onOk: () => {
                                formData.prizeList = [];
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                      </div>
                    )}
                  />
                )}
              </Table>
            )}
            {formData.prizeList.length <= 0 && (
              <Button
                type="primary"
                onClick={() => {
                  setShow(true);
                  setEditValue({});
                }}
              >
                选择实物
              </Button>
            )}
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog title={false} v2 width={700} footer={false} visible={show} onClose={() => setShow(false)}>
        <JdProduct
          hasProbability={false}
          hasLimit={false}
          onChange={onSubmit}
          onCancel={() => setShow(false)}
          editValue={editValue}
          prizeNameLength={10}
          width={200}
          height={200}
        />
      </LzDialog>
    </div>
  );
};
