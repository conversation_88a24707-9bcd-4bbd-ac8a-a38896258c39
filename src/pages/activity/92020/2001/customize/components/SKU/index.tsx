/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="曝光商品背景">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="模块背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  height={1000}
                  value={formData.skuModuleBg}
                  onChange={(skuModuleBg) => {
                    setForm({ skuModuleBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、高度1000px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ skuModuleBg: defaultValue?.skuModuleBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="商品背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={376}
                  height={415}
                  value={formData.skuItemBg}
                  onChange={(skuItemBg) => {
                    setForm({ skuItemBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度376px、高度415px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ skuItemBg: defaultValue?.skuItemBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>

          <Form.Item label="按钮">
            <LzColorPicker value={formData.skuBtnColor} onChange={(skuBtnColor) => setForm({ skuBtnColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ skuBtnColor: defaultValue?.skuBtnColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
