/**
 * Author: lrh
 * Date: 2025-08-20 18:02
 * Description: 悬浮图标
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Switch, Input } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';
import { deepCopy } from '@/utils';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="悬浮图标">
        <Form {...formLayout} className={styles.form}>
          <Form.Item required label="是否开启悬浮图标">
            <Switch
              checked={formData.isShowSuspend}
              autoWidth
              checkedChildren={'已启用'}
              unCheckedChildren={'已关闭'}
              onChange={(isShowSuspend) => {
                formData.isShowSuspend = isShowSuspend;
                formData.suspendImg = '';
                formData.suspendLink = '';
                setForm(formData);
              }}
            />
          </Form.Item>
          {formData.isShowSuspend && (
            <>
              <Form.Item label="图标" required>
                <Grid.Row>
                  <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                    <LzImageSelector
                      width={150}
                      height={150}
                      value={formData.suspendImg}
                      onChange={(suspendImg) => {
                        setForm({ suspendImg });
                      }}
                    />
                  </Form.Item>
                  <Form.Item style={{ marginBottom: 0 }}>
                    <div className={styles.tip}>
                      <p>图片尺寸：宽度150px、高度150px</p>
                      <p>图片大小：不超过1M</p>
                      <p>图片格式：JPG、JPEG、PNG、GIF</p>
                    </div>
                    <div>
                      <Button
                        type="primary"
                        text
                        onClick={() => {
                          setForm({ suspendImg: '' });
                        }}
                      >
                        重置
                      </Button>
                    </div>
                  </Form.Item>
                </Grid.Row>
              </Form.Item>
              <Form.Item required label="跳转链接">
                <Grid.Row>
                  <Input
                    value={formData.suspendLink}
                    onChange={(suspendLink) => {
                      setForm({ suspendLink });
                    }}
                    className="w-300"
                    placeholder="请输入跳转链接"
                  />
                </Grid.Row>
              </Form.Item>
            </>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
