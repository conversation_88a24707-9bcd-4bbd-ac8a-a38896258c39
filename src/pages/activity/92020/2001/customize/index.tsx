/**
 * Author: lrh
 * Date: 2025-08-20 11:02
 * Description: 设置模板
 */
import React from 'react';
import { CustomValue } from '../util';
import LzTipPanel from '@/components/LzTipPanel';
// 基础信息
import Base from './components/Base';
import Prize from './components/Prize';
import SKU from './components/SKU';
import Step from './components/Step';
import SuspendIcon from './components/SuspendIcon';

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  target: number;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { target, defaultValue, value, handleChange } = props;
  console.log('target', target);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <LzTipPanel message="自定义模板时，点击样例图的各区域，进行素材的更换" />
      {<Base {...eventProps} />}
      {<SuspendIcon {...eventProps} />}
      {<SKU {...eventProps} />}
      {/* {<Step {...eventProps} />} */}
    </div>
  );
};
