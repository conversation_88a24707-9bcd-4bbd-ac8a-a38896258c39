/**
 * 92017 记录
 */
import React, { useEffect, useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LockEquityRecord from './components/LockEquityRecord';
import WinRecord from './components/WinRecord';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  useEffect(() => {
    // getNum();
  }, []);

  const [activeKey, setActiveKey] = useState('1');
  return (
    // {`${getParams('name') ? getParams('name') : '全渠道新客礼2.0'}活动报表`}
    <div className="crm-container">
      <LzPanel title="活动记录" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="锁权记录" key="1">
            <LockEquityRecord />
          </Tab.Item>
          <Tab.Item title="中奖记录" key="2">
            <WinRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
