import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataOrderRecord, dataOrderRecordExport } from '@/api/v92020';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import dayjs from "dayjs";

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [timeRange, setTimeRange] = useState([
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD HH:mm:ss'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD HH:mm:ss'),
  ]);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataOrderRecord(query)
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataOrderRecordExport(formValue).then((data: any) => downloadExcel(data, '锁权记录'));
  };

  const encryptStr = (pin: string): string => {
    const first = pin.slice(0, 1);
    const last = pin.slice(-1);
    return `${first}****${last}`;
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <Form.Item name="nickName" label="昵称">
          <Input placeholder="请输入用户昵称" />
        </Form.Item>
        <FormItem name="joinTimeList" label="活动时间">
          <RangePicker
            hasClear={false}
            defaultValue={timeRange}
            showTime
            value={timeRange}
            onChange={(val: any) =>
              setTimeRange([val[0].format('YYYY-MM-DD HH:mm:ss'), val[1].format('YYYY-MM-DD HH:mm:ss')])
            }
            format="YYYY-MM-DD HH:mm:ss"
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const timeRange = [
                dayjs(getParams('startTime')).format('YYYY-MM-DD HH:mm:ss'),
                dayjs(getParams('endTime')).format('YYYY-MM-DD HH:mm:ss'),
              ];
              // 更新状态
              setTimeRange(timeRange);
              // 同步到表单字段
              field.setValue('joinTimeList', timeRange);

              loadData({ ...field.getValues(), ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column title="会员等级" dataIndex="level" />
        <Table.Column
          title="参与时间"
          cell={(value, index, data) => <div>{dayJs(data.accessTime).format('YYYY-MM-DD HH:mm:ss')}</div>}
        />

        <Table.Column title="消耗积分" dataIndex="points" />
        <Table.Column title="锁权状态" dataIndex="receiveStatus" />
        <Table.Column title="备注" dataIndex="receiveFailReason" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
