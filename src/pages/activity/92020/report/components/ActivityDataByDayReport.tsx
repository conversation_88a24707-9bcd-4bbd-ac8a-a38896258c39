import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataDailyData, dataDailyDataExport } from '@/api/v92020';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [timeRange, setTimeRange] = useState([
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ]);
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataDailyData(query)
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataDailyDataExport(formValue).then((data: any) => downloadExcel(data, '活动数据报表'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="活动时间">
          <RangePicker
            hasClear={false}
            defaultValue={timeRange}
            showTime
            value={timeRange}
            onChange={(val: any) => setTimeRange([val[0].format('YYYY-MM-DD'), val[1].format('YYYY-MM-DD')])}
            format="YYYY-MM-DD"
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const newTimeRange = [
                dayjs(getParams('startTime')).format('YYYY-MM-DD'),
                dayjs(getParams('endTime')).format('YYYY-MM-DD'),
              ];
              // 更新状态
              setTimeRange(newTimeRange);
              // 同步到表单字段
              field.setValue('dateRange', newTimeRange);

              loadData({ ...field.getValues(), ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="日期" dataIndex="dataTime" width={120} />
        <Table.Column title="访问次数" dataIndex="pv" />
        <Table.Column title="访问人数" dataIndex="uv" />
        <Table.Column title="活动入会人数" dataIndex="openCardUser" />
        <Table.Column title="参与锁权人数" dataIndex="lockUser" />
        <Table.Column title="锁权率" dataIndex="lockRate" />
        <Table.Column title="满足活动金额门槛用户人数" dataIndex="successUser" />
        <Table.Column title="兑换好礼人数" dataIndex="prizeUser" />
        <Table.Column title="填写收货地址人数" dataIndex="addressUser" />
        <Table.Column title="锁权用户购买商品人数" dataIndex="lockOrderUser" />
        <Table.Column title="锁权购买率" dataIndex="lockOrderRate" />
        <Table.Column title="锁权购买商品次数" dataIndex="lockOrderNum" />
        <Table.Column title="锁权用户购买金额" dataIndex="lockOrderPrice" />
        <Table.Column title="满足门槛订单笔数" dataIndex="successUserOrderNum" />
        <Table.Column title="满足活动金额门槛用户购买金额" dataIndex="successUserOrderPrice" />
        <Table.Column title="活动入会用户下单人数" dataIndex="memberUser" />
        <Table.Column title="活动入会用户下单金额" dataIndex="memberPrice" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
