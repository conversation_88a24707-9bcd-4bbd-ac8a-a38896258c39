/**
 * 大转盘抽奖数据报表
 */
import React, { useEffect, useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import ActivityDataByDayReport from './components/ActivityDataByDayReport';
import ProductSeriesReport from './components/ProductSeriesReport';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="数据报表" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="单活动数据" key="1">
            <ActivityDataByDayReport />
          </Tab.Item>
          <Tab.Item title="品线数据" key="2">
            <ProductSeriesReport />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
