import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { userActRecordPage, userActRecordPageExport } from '@/api/v92019Data';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [timeRange, setTimeRange] = useState([
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ]);
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    userActRecordPage(query)
      .then((res: any): void => {
        setTableData(res as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    userActRecordPageExport(formValue).then((data: any) => downloadExcel(data, '活动数据报表'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="活动时间">
          <RangePicker
            hasClear={false}
            defaultValue={timeRange}
            showTime
            value={timeRange}
            onChange={(val: any) => setTimeRange([val[0].format('YYYY-MM-DD'), val[1].format('YYYY-MM-DD')])}
            format="YYYY-MM-DD"
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const newTimeRange = [
                dayjs(getParams('startTime')).format('YYYY-MM-DD'),
                dayjs(getParams('endTime')).format('YYYY-MM-DD'),
              ];
              // 更新状态
              setTimeRange(newTimeRange);
              // 同步到表单字段
              field.setValue('dateRange', newTimeRange);

              loadData({ ...field.getValues(), ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="日期" dataIndex="dataTime" width={120} />
        <Table.Column title="pv" dataIndex="pv" width={80} />
        <Table.Column title="uv" dataIndex="uv" width={80} />
        <Table.Column title="成功兑换奖品人数" dataIndex="receiveUser" />
        <Table.Column title="成功兑换用户完成的订单GMV" dataIndex="successGmv" />
        <Table.Column title="访问活动用户完成订单GMV（uv的订单GMV）" dataIndex="uvGmv" />
        <Table.Column title="新开卡人数" dataIndex="openCardUser" width={120} />

        {tableData[0]?.couponList?.map((item, index) => (
          <Table.Column
            title={item.prizeName}
            cell={(value, idx, data) => <div>{data.couponList[index].exchangeNum}</div>}
          />
        ))}

        {/* {tableData?.couponList?.map((item, index) => ( */}
        {/*  <Table.Column */}
        {/*    title={item.prizeName} */}
        {/*    key={index} */}
        {/*    cell={(value, index, data) => <div>{item.exchangeNum}</div>} */}
        {/*  /> */}
        {/* ))} */}
      </Table>
    </div>
  );
};
