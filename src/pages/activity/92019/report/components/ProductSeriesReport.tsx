import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { step, stepExport } from '@/api/v92019Data';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [timeRange, setTimeRange] = useState([
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ]);
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    step(query)
      .then((res: any): void => {
        setTableData(res as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    stepExport(formValue).then((data: any) => downloadExcel(data, '品线数据报表'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="活动时间">
          <RangePicker
            hasClear={false}
            defaultValue={timeRange}
            showTime
            value={timeRange}
            onChange={(val: any) => setTimeRange([val[0].format('YYYY-MM-DD'), val[1].format('YYYY-MM-DD')])}
            format="YYYY-MM-DD"
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const newTimeRange = [
                dayjs(getParams('startTime')).format('YYYY-MM-DD'),
                dayjs(getParams('endTime')).format('YYYY-MM-DD'),
              ];
              // 更新状态
              setTimeRange(newTimeRange);
              // 同步到表单字段
              field.setValue('dateRange', newTimeRange);

              loadData({ ...field.getValues(), ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="兑换人数" dataIndex="exchangeUser" width={120} />
        <Table.Column title="gmv" dataIndex="gmv" />
        <Table.Column title="开卡人数" dataIndex="openCardUser" />
        <Table.Column title="品线" dataIndex="series" />
        <Table.Column title="段位" dataIndex="step" />
        <Table.Column title="整体uv" dataIndex="uv" />
      </Table>
    </div>
  );
};
