/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 获奖名单
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Grid, Radio } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '@/pages/activity/91012/2001/util';

const RadioGroup = Radio.Group;
const formLayout: Omit<FormLayout, 'labelAlign' | 'wrapperCol'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);

  return (
    <div className={styles.award}>
      <LzPanel title="聚合弹窗关闭配置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="是否可关闭">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <RadioGroup
                  value={formData.canNotCloseJoinPopup}
                  defaultValue={'1'}
                  onChange={(canNotCloseJoinPopup: number) => setForm({ canNotCloseJoinPopup })}
                >
                  <Radio id="1" value={'1'}>
                    可关闭
                  </Radio>
                  <Radio id="2" value={'2'}>
                    不可关闭
                  </Radio>
                </RadioGroup>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
