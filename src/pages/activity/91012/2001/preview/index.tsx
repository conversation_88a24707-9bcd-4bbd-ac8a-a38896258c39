/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import format from '@/utils/format';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        <FormItem label="活动生成人群包">{`${formData.crowdPackage === 1 ? '开启' : '关闭'}`}</FormItem>
        {/* <FormItem label="新单类型">{formData.firstBuyType === 0 ? '会员新单' : '店铺新单'}</FormItem> */}
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderEndTime)}`}</FormItem>
        <FormItem label="历史订单时间">{`${format.formatDateTimeDayjs(
          formData.hisOrderStartTime,
        )}至${format.formatDateTimeDayjs(formData.hisOrderEndTime)}`}</FormItem>
        <FormItem label="订单状态">{formData.orderRestrainStatus === 1 ? '已完成' : '已付款'}</FormItem>
        <FormItem label="价格类型">{formData.priceType === 0 ? '京东价' : '实付价'}</FormItem>
        {/* <FormItem label="奖品延迟发放">{formData.awardDays > 0 ? `延迟发放${formData.awardDays}天` : '否'}</FormItem> */}
        {/* <FormItem label="价格类型">京东价</FormItem> */}
        <FormItem label="订单金额">大于等于{formData.orderRestrainAmount}元</FormItem>
        <FormItem label="订单商品">
          {formData.orderSkuExposure === 0 && <div>全部商品</div>}
          {(formData.orderSkuExposure === 1 || formData.orderSkuExposure === 2) && (
            <>
              {formData.orderSkuExposure === 1 && <div>指定商品</div>}
              {formData.orderSkuExposure === 2 && <div>排除商品</div>}
              <SkuList skuList={formData.orderSkuList} />
            </>
          )}
        </FormItem>
        <FormItem label="复购权益一奖品设置" isPreview={false}>
          <Table
            dataSource={formData.prizeListOne.filter((e) => e.prizeName !== '谢谢参与')}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
            <Table.Column title="奖项类型" cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>} />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="复购权益二奖品设置" isPreview={false}>
          <Table
            dataSource={formData.prizeListTwo.filter((e) => e.prizeName !== '谢谢参与')}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项名称" width={200} dataIndex="prizeName" />
            <Table.Column title="奖项类型" cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>} />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="是否添加曝光商品">
          {formData.isExposure === 0 && <div>否</div>}
          {formData.isExposure === 1 && <SkuList skuList={formData.skuList} />}
        </FormItem>
        {/* <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem> */}
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
