import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Select, Table } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataPartakeLog, dataPartakeLogExport, dataPartakeLogUploadPin } from '@/api/v91004';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';
import styles from '../style.module.scss';

import dayjs from 'dayjs';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

// isWin	1待发放  2 已失效 3已领取 5发放失败
const IS_WIN = [
  { label: '全部', value: '' },
  { label: '待发放', value: 1 },
  { label: '已失效', value: 2 },
  { label: '已领取', value: 3 },
  { label: '发放失败', value: 5 },
];
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [showAddressDialog, setShowAddressDialog] = useState(false);
  const [addressData, setAddressData] = useState(null);
  const defaultRangeVal = [
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataPartakeLog(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataPartakeLogExport(formValue).then((data: any) => downloadExcel(data, '参与记录'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);
  // 查看地址
  const queryAddress = (rowData) => {
    // console.log('查看地址', rowData.addressResponse);
    setAddressData(rowData.addressResponse);
    setShowAddressDialog(true);
  };
  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="receiveStatus" label="发放状态" requiredMessage="请选择发放状态">
          <Select followTrigger mode="single" showSearch hasClear style={{ marginRight: 8 }} dataSource={IS_WIN} />
        </FormItem>
        <FormItem name="dateRange" label="领取时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
          生成人群包
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="序号" dataIndex="num" />
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="领取时间"
          dataIndex="winningTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.winningTime)}</div>}
        />
        <Table.Column
          title="奖品发放状态"
          dataIndex="receiveStatus"
          // cell={(value, index, data) => <div>{IS_WIN.find((it) => it.value === data.receiveStatus)?.label}</div>}
        />
        <Table.Column title="奖品名称" dataIndex="prizeInfo" />

        <Table.Column
          title="操作"
          dataIndex=""
          cell={(data, index, row: any) => {
            return (
              <div>
                {row.prizeType === 3 && (
                  <Button
                    text
                    onClick={() => {
                      queryAddress(row);
                    }}
                  >
                    查看地址
                  </Button>
                )}
                {row.prizeType !== 3 && <div>--</div>}
              </div>
            );
          }}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      <div>
        <LzDialog
          title="生成人群包"
          className="lz-dialog-mini"
          visible={packVisible}
          footer={false}
          onCancel={() => setPackVisible(false)}
          onClose={() => setPackVisible(false)}
        >
          <LzGenerateCrowdBag
            dataUploadPin={dataPartakeLogUploadPin}
            formValue={field.getValues()}
            cancel={() => setPackVisible(false)}
          />
        </LzDialog>
      </div>
      <div>
        <LzDialog
          title="地址详情"
          className="lz-dialog-mini"
          visible={showAddressDialog}
          footer={false}
          onCancel={() => setShowAddressDialog(false)}
          onClose={() => setShowAddressDialog(false)}
        >
          <Form>
            <FormItem label="姓名：" className={styles.addressDiaItem}>
              {addressData?.realName}
            </FormItem>
            <FormItem label="电话：" className={styles.addressDiaItem}>
              {addressData?.mobile}
            </FormItem>
            <FormItem label="省市区：" className={styles.addressDiaItem}>
              {addressData?.province}
              {addressData?.city}
              {addressData?.county}
            </FormItem>
            <FormItem label="地址：" className={styles.addressDiaItem}>
              {addressData?.address}
            </FormItem>
          </Form>
        </LzDialog>
      </div>
    </div>
  );
};
