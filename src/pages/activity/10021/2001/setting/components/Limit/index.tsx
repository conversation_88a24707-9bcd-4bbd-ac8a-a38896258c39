/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 14:58
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import { Form, Field, NumberPicker, Radio, Grid } from '@alifd/next';
import styles from '../../style.module.scss';
import LzPanel from '@/components/LzPanel';
import { activityEditDisabled } from '@/utils';
import { FormLayout, PageData } from '../../../util';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useImperativeHandle(sRef, (): { submit: () => any } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="中奖限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          {/* <Form {...formItemLayout} field={field}> */}
          <FormItem label="每人每天最多中奖" required>
            <RadioGroup
              value={formData.winLotteryDayType}
              onChange={(winLotteryDayType: number) => setData({ winLotteryDayType })}
            >
              <Radio id="1" value={1}>
                不限制
              </Radio>
              <Radio id="2" value={2}>
                限制
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label=" " colon={false}>
            <Row gutter="4">
              <Col>
                {formData.winLotteryDayType === 2 && (
                  <div className={styles.panel}>
                    <FormItem
                      name="winLotteryDayCounts"
                      required
                      requiredTrigger="onBlur"
                      requiredMessage="请输入每人每天最多中奖次数"
                      style={{ margin: 0 }}
                    >
                      <NumberPicker
                        value={formData.winLotteryDayCounts}
                        onChange={(winLotteryDayCounts: number) => setData({ winLotteryDayCounts })}
                        type="inline"
                        min={1}
                        max={9999999}
                        className={styles.number}
                      />
                      限制每天内用户最多中奖{formData.winLotteryDayCounts}次
                    </FormItem>
                  </div>
                )}
                {formData.winLotteryDayType === 1 && <div className={styles.panel}>不限制每天用户最多中奖次数</div>}
                <div className={styles.tip}>注： 限制每人每天最多中奖次数 </div>
              </Col>
            </Row>
          </FormItem>
          <FormItem label="每人累计最多中奖" required>
            <RadioGroup
              value={formData.winLotteryTotalType}
              onChange={(winLotteryTotalType: number) => setData({ winLotteryTotalType })}
            >
              <Radio id="1" value={1}>
                不限制
              </Radio>
              <Radio id="2" value={2}>
                限制
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label=" " colon={false}>
            <Row gutter="4">
              <Col>
                {formData.winLotteryTotalType === 2 && (
                  <div className={styles.panel}>
                    <FormItem
                      name="winLotteryTotalCounts"
                      required
                      requiredTrigger="onBlur"
                      requiredMessage="请输入限制活动周期内用户最多中奖次数"
                      style={{ margin: 0 }}
                    >
                      <NumberPicker
                        value={formData.winLotteryTotalCounts}
                        onChange={(winLotteryTotalCounts: number) => setData({ winLotteryTotalCounts })}
                        type="inline"
                        min={1}
                        max={9999999}
                        className={styles.number}
                      />
                      限制活动周期内用户最多中奖{formData.winLotteryTotalCounts}次
                    </FormItem>
                  </div>
                )}
                {formData.winLotteryTotalType === 1 && (
                  <div className={styles.panel}>不限制活动周期内用户最多中奖次数</div>
                )}
                <div className={styles.tip}>注： 限制活动期间内每人累计最多中奖次数</div>
              </Col>
            </Row>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
