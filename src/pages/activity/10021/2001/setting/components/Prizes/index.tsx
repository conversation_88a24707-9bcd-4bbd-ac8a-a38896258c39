/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useEffect, useImperativeHandle } from 'react';
import { Form, Field } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData, PRIZE_INFO, PrizeInfo } from '../../../util';
import LzPrize from '@/components/LzPrizeHasEdit';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.prizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.prizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 8 - prizeListLength; i++) {
      list.push(PRIZE_INFO);
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({ prizeList: list.length ? list : formData.prizeList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem isPreview label="单次中奖总概率">
            {formData.totalProbability.toFixed(3)}%
          </FormItem>
          <FormItem label="奖品列表" required>
            <LzPrize
              defaultValue={defaultValue}
              defaultTarget={3}
              typeList={[3, 1, 4]}
              formData={formData}
              onChange={(data) => setData(data)}
            />
            {/* <LzPrizeByLevel formData={formData} onChange={(data) => setData(data)} /> */}
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
