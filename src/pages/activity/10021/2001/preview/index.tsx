/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import { TASK_TYPE, renderTaskGift, renderTaskContent, renderTaskLength } from '@/utils/taskRender';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import SkuDialog from '@/components/SkuDialog';
import { renderTaskDate } from '@/utils/taskGameRender';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [goodDialog, setGoodDialog] = useState(false);
  const [currentGood, setCurrentGood] = useState<any>({});
  const showGoods = (good): void => {
    console.log(good);
    setCurrentGood(good);
    setGoodDialog(true);
  };

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="免费赠送">
          {`${formData.chanceType === 1 ? '初始赠送' : '每人每天赠送'}${formData.chance}次 `}
        </FormItem>
        <FormItem label="任务获取">
          {formData.isShowTask === 1 ? '设置' : '不设置'}
          {formData.isShowTask === 1 && (
            <Table dataSource={formData.taskList} style={{ marginTop: '15px' }}>
              <Table.Column title="任务" cell={(_, index) => <div>{`任务${index + 1}`}</div>} />
              <Table.Column title="任务标题" cell={(val, index, _) => TASK_TYPE[_.taskType]} />
              <Table.Column
                title="任务内容"
                cell={(val, index, _) => {
                  const content = renderTaskContent(val, index, _);
                  return (
                    <div>
                      {content}
                      {[3, 5, 6, 7, 8, 9].includes(_.taskType) && _.skuList.length > 0 && (
                        <div style={{ color: '#1677ff', cursor: 'pointer' }} onClick={() => showGoods(_)}>
                          商品展示
                        </div>
                      )}
                    </div>
                  );
                }}
              />
              <Table.Column title="完成任务次数" cell={(val, index, _) => renderTaskLength(val, index, _)} />
              <Table.Column title="任务奖励" cell={(val, index, _) => renderTaskGift(val, index, _)} />
              <Table.Column
                width={175}
                title="任务时间"
                cell={(val, index: number, data) => renderTaskDate(val, index, data)}
              />
            </Table>
          )}
        </FormItem>
        <FormItem label="奖品列表" isPreview={false}>
          <Table
            dataSource={formData.prizeList.filter((e) => e.prizeName !== '谢谢参与')}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="中奖概率(%)"
              cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>}
            />
            <Table.Column
              title="每日发放限额"
              cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
            />
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="每人每天最多获奖次数">
          {formData.winLotteryDayType === 1 && '不限制'}
          {formData.winLotteryDayType === 2 && `限制每天内用户最多中奖${formData.winLotteryDayCounts}次`}
        </FormItem>
        <FormItem label="每人累计最多中奖次数">
          {formData.winLotteryTotalType === 1 && '不限制'}
          {formData.winLotteryTotalType === 2 && `限制活动周期内用户最多中奖${formData.winLotteryTotalCounts}次`}
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
      <SkuDialog visible={goodDialog} onClose={() => setGoodDialog(false)} skuList={currentGood.skuList} />
    </div>
  );
};
