/**
 * Author: zhang<PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useState, useEffect } from 'react';
import { Form, NumberPicker, Field, Radio, Button, Icon, Table, Grid } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import LzMsg from '@/components/LzMsg';
import { PageData, FormLayout } from '../../../util';
import { renderTaskGift, renderTaskContent, renderTaskLength, TASK_TYPE } from '@/utils/taskRender';
import { activityEditDisabled } from '@/utils';
import LzTask from '@/components/LzTask';
import { renderTaskDate } from '@/utils/taskGameRender';

const { Row, Col } = Grid;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 添加任务弹窗
  const [visible, setVisible] = useState<boolean>(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState<any>(null);
  // 当前编辑行数据
  const [initValue, setInitValue] = useState<any>(null);
  // 当前编辑index
  const [target, setTarget] = useState<number | null>(null);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data }, 'task');
  };
  /**
   * 添加任务组件成功回调
   * 向任务列表中添加数据 & 同步数据
   * @param taskInfo 任务信息
   */
  const handleSubmit = (taskInfo: any): void => {
    const { taskList } = formData;
    // 将taskLength区分为每日完成任务上限&活动内完成任务上线
    taskInfo = {
      ...taskInfo,
      totalLimit: taskInfo.taskLength,
      dailyLimit: taskInfo.dailyLimit || taskInfo.taskLength,
    };
    // 获取表格中是否已经存在同类型任务
    const result = taskList.filter((e): boolean => e.taskType === taskInfo.taskType);
    // 不存在同类型 & 新增
    if (!result.length && target === null) {
      taskList.push(taskInfo);
    } else if (target !== null) {
      // 编辑任务
      taskList[target] = taskInfo;
    } else {
      LzMsg.error('已添加当前任务类型，请勿重新添加');
    }
    setData({ taskList });
    setVisible(false);
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="抽奖机会">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          {/* <Form {...formItemLayout} field={field}> */}
          <Form.Item label="免费赠送" required>
            <Radio.Group
              value={formData.chanceType}
              onChange={(chanceType: number) => setData({ chanceType, chance: 0 })}
            >
              <Radio id="1" value={1}>
                初始赠送
              </Radio>
              <Radio id="2" value={2}>
                每人每天赠送
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label=" " colon={false}>
            <Row gutter="4">
              <Col>
                <Form.Item name="chance" required requiredTrigger="onBlur" requiredMessage="请输入赠送次数">
                  <NumberPicker
                    min={0}
                    max={9999999}
                    type="inline"
                    value={formData.chance}
                    onChange={(chance: number) => setData({ chance })}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form.Item>
          <Form.Item label="任务获取" required>
            <RadioGroup
              disabled={activityEditDisabled()}
              value={formData.isShowTask}
              onChange={(isShowTask: number) => {
                if (isShowTask === 2) {
                  formData.taskList = [];
                }
                formData.isShowTask = isShowTask;
                setData(formData);
              }}
            >
              <Radio id="2" value={2}>
                不设置
              </Radio>
              <Radio id="1" value={1}>
                设置
              </Radio>
            </RadioGroup>
          </Form.Item>
          {formData.isShowTask === 1 && (
            <Form.Item label=" " colon={false} required requiredMessage="请添加任务">
              <Button
                type="primary"
                onClick={(): void => {
                  setTarget(null);
                  setEditValue(null);
                  setVisible(true);
                }}
                disabled={formData.taskList.length >= 5 || activityEditDisabled()}
              >
                <Icon type="add" />
                添加任务
              </Button>
              {formData.taskList.length > 0 && (
                <Table dataSource={formData.taskList} style={{ marginTop: '15px' }}>
                  <Table.Column title="任务" cell={(_, index: number) => <div>{`任务${index + 1}`}</div>} />
                  <Table.Column title="任务标题" cell={(val, index: number, _) => TASK_TYPE[_.taskType]} />
                  <Table.Column title="任务内容" cell={(val, index: number, _) => renderTaskContent(val, index, _)} />
                  <Table.Column
                    title="完成任务次数"
                    cell={(val, index: number, _) => renderTaskLength(val, index, _)}
                  />
                  <Table.Column title="任务奖励" cell={(val, index: number, _) => renderTaskGift(val, index, _)} />
                  <Table.Column
                    width={175}
                    title="任务时间"
                    cell={(val, index: number, data) => renderTaskDate(val, index, data)}
                  />
                  <Table.Column
                    title="操作"
                    width={150}
                    cell={(val, index: number, _) => (
                      <div>
                        <Button
                          text
                          type="primary"
                          onClick={(): void => {
                            setEditValue(formData.taskList[index]);
                            setInitValue(defaultValue.taskList[index]);
                            setTarget(index);
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        {!activityEditDisabled() && (
                          <Button
                            text
                            type="primary"
                            onClick={(): void => {
                              formData.taskList.splice(index, 1);
                              setData(formData.taskList);
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        )}
                        {!activityEditDisabled() && formData.taskList.length > 0 && index > 0 && (
                          <Button
                            text
                            onClick={(): void => {
                              formData.taskList.splice(
                                index - 1,
                                1,
                                ...formData.taskList.splice(index, 1, formData.taskList[index - 1]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-35`} />
                          </Button>
                        )}
                        {!activityEditDisabled() &&
                          formData.taskList.length > 0 &&
                          index < formData.taskList.length - 1 && (
                            <Button
                              text
                              onClick={(): void => {
                                formData.taskList.splice(
                                  index,
                                  1,
                                  ...formData.taskList.splice(index + 1, 1, formData.taskList[index]),
                                );
                                setData(formData);
                              }}
                            >
                              <i className={`iconfont icon-iconjiantou-34`} />
                            </Button>
                          )}
                      </div>
                    )}
                  />
                </Table>
              )}
            </Form.Item>
          )}
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        className="lz-dialog-small"
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
      >
        <LzTask
          initValue={initValue}
          typeList={[2, 26, 4, 5, 6, 7, 8, 9, 10, 12, 21, 27]}
          editValue={editValue}
          formData={formData}
          onCancel={() => setVisible(false)}
          onSubmit={handleSubmit}
        />
      </LzDialog>
    </div>
  );
};
