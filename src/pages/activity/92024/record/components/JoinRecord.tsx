import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Select, Field, Table, Button, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataGetOrder, dataPartakeLog, dataPartakeLogExport, dataPartakeLogUploadPin } from '@/api/v92024';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

const IS_WIN = [
  { label: '全部', value: '' },
  { label: '已领取', value: 1 },
  { label: '已取消', value: 2 },
];

const PRIZE_TYPE = [
  { label: '全部', value: '' },
  { label: '京元宝权益', value: 12 },
  { label: '京豆', value: 2 },
  { label: '优惠券', value: 1 },
  { label: '实物', value: 3 },
  { label: '积分', value: 4 },
  { label: '红包', value: 6 },
  { label: '礼品卡', value: 7 },
  { label: '京东E卡', value: 8 },
  { label: 'PLUS会员卡', value: 9 },
  { label: '爱奇艺会员卡', value: 10 },
];
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [orderVisible, setOrderVisible] = useState(false);
  const [orderData, setOrderData] = useState([]);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataPartakeLog(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataPartakeLogExport(formValue).then((data: any) => downloadExcel(data, '下单阶梯有礼参与记录'));
  };
  const viewOrder = (id) => {
    setLoading(true);
    dataGetOrder({ id })
      .then((res: any) => {
        setOrderData(res);
        setLoading(false);
        setOrderVisible(true);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <Form.Item name="prizeName" label="奖品名称">
          <Input placeholder="请输入奖品名称" />
        </Form.Item>
        <FormItem name="prizeType" label="奖品类型" requiredMessage="请选择奖品类型">
          <Select
            followTrigger
            mode="single"
            defaultValue={''}
            showSearch
            hasClear
            style={{ marginRight: 8 }}
            dataSource={PRIZE_TYPE}
          />
        </FormItem>
        <FormItem name="receiveStatus" label="领取状态" requiredMessage="请选择领取状态">
          <Select
            followTrigger
            mode="single"
            defaultValue={''}
            showSearch
            hasClear
            style={{ marginRight: 8 }}
            dataSource={IS_WIN}
          />
        </FormItem>
        <FormItem name="dateRange" label="参与时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
          生成人群包
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.pin ? Utils.mask(row.pin) : '-'}
                  {row.pin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.pin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="参与时间"
          dataIndex="drawTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.winningTime)}</div>}
        />
        <Table.Column title="奖品发放状态" dataIndex="receiveStatus" />
        <Table.Column title="奖品类型" dataIndex="prizeType" />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column
          title="订单详情"
          cell={(value, index, data) => (
            <Button type={'primary'} text onClick={() => viewOrder(data.id)}>
              查看订单详情
            </Button>
          )}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      <LzDialog
        title="生成人群包"
        className="lz-dialog-mini"
        visible={packVisible}
        footer={false}
        onCancel={() => setPackVisible(false)}
        onClose={() => setPackVisible(false)}
      >
        <LzGenerateCrowdBag
          dataUploadPin={dataPartakeLogUploadPin}
          formValue={field.getValues()}
          cancel={() => setPackVisible(false)}
        />
      </LzDialog>
      <LzDialog
        title="订单详情"
        className="lz-dialog-mini"
        visible={orderVisible}
        footer={false}
        onCancel={() => setOrderVisible(false)}
        onClose={() => setOrderVisible(false)}
      >
        <Table dataSource={orderData}>
          <Table.Column title="订单编号" dataIndex="orderId" />
          <Table.Column title="订单完成时间" dataIndex="orderFinishTime" />
          <Table.Column
            title="订单金额"
            dataIndex="orderAmount"
            cell={(value, index, data) => (
              <div>{data.orderAmount ? parseFloat(data.orderAmount).toFixed(2) : data.orderAmount}</div>
            )}
          />
          <Table.Column title="订单状态" dataIndex="orderStatus" />
        </Table>
      </LzDialog>
    </div>
  );
};
