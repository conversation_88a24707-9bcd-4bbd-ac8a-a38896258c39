/**
 * 大转盘抽奖数据报表
 */
import React, { useState } from 'react';
import { Tab, Button } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import JoinRecord from './components/JoinRecord';
import WinRecord from './components/WinRecord';
import { dataPartakeLogExport, dataWinningLogExport } from '@/api/v92024';
import exportCombinedLogs from '@/utils/exportAll';
import LzDocGuide from "@/components/LzDocGuide";

export default () => {
  const [activeKey, setActiveKey] = useState('1');

  return (
    <div className="crm-container">
      <LzPanel title="下单阶梯有礼数据报表" actions={<LzDocGuide />}>
        <Button
          onClick={() => {
            exportCombinedLogs([dataPartakeLogExport, dataWinningLogExport], '下单阶梯有礼数据报表');
          }}
          style={{ float: 'right', position: 'relative', zIndex: 1000, marginTop: 8 }}
        >
          导出全部
        </Button>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="参与记录" key="1">
            <JoinRecord />
          </Tab.Item>
          <Tab.Item title="中奖记录" key="2">
            <WinRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
