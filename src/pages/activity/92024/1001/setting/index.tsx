/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:03
 * Description: 活动设置
 */
import React, { useImperativeHandle, useRef, useReducer } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 曝光商品
import ExposureInfo from './components/Exposure';
// 参与规则
import LimitInfo from './components/Limit';
// 分享设置
import ShareInfo from './components/Share';
// 活动规则
import RulesInfo from './components/Rules';
import { PageData, CustomValue } from '@/pages/activity/92024/1001/util';

interface Props {
  onSettingChange: (activityInfo: PageData) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}
interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const onChange = (activityInfo): void => {
    console.log('活动设置信息更新:', activityInfo);
    setFormData(activityInfo);
    onSettingChange(activityInfo);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const exposureRef = useRef<{ submit: () => void | null }>(null);
  const limitRef = useRef<{ submit: () => void | null }>(null);
  const shareRef = useRef<{ submit: () => void | null }>(null);
  const rulesRef = useRef<{ submit: () => void | null }>(null);

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current,
        exposureRef.current,
        limitRef.current,
        shareRef.current,
        rulesRef.current,
      ];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        const result = events[index].submit();
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  return (
    <div className={styles.setting}>
      <BaseInfo sRef={baseRef} {...settingProps} />
      <ExposureInfo sRef={exposureRef} {...settingProps} />
      <LimitInfo sRef={limitRef} {...settingProps} />
      <ShareInfo sRef={shareRef} {...settingProps} decoValue={decoValue} />
      <RulesInfo sRef={rulesRef} {...settingProps} />
    </div>
  );
};
