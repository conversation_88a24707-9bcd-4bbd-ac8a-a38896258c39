.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  margin-top: 15px;
  .skuContainer {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;
    &:hover {
      .del {
        display: block;
      }
    }

    .skuImg {
      width: 60px;
      height: 60px;
      margin-right: 10px;
    }
    .skuName {
      max-width: 120px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .skuId {
      color: lightgray;
      font-size: 12px;
    }
    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}
.del {
  position: absolute;
  right: -5px;
  top: -5px;
  line-height: 16px;
  color: #9ca7b6;
  display: none;
  cursor: pointer;
}
