/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Input, Table } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import dayjs from 'dayjs';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${dayjs(formData.rangeDate[0]).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(
          formData.rangeDate[1],
        ).format('YYYY-MM-DD HH:mm:ss')}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="是否添加曝光商品">
          {formData.isExposure === 0 && <div>否</div>}
          {formData.isExposure === 1 && (
            <SkuList skuList={formData.skuList} />
            //
            // <div className={styles.container}>
            //   {formData.skuList?.map((sku, index) => {
            //     return (
            //       <div className={styles.skuContainer}>
            //         <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
            //         <div>
            //           <div className={styles.skuName}>{sku.skuName}</div>
            //           <div className={styles.skuId}>SKUID:{sku.skuId}</div>
            //           <div className={styles.price}>¥ {sku.jdPrice}</div>
            //         </div>
            //       </div>
            //     );
            //   })}
            // </div>
          )}
        </FormItem>
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderRangeData[0],
        )}至${format.formatDateTimeDayjs(formData.orderRangeData[1])}`}</FormItem>
        <FormItem label="价格类型">{formData.moneyType === 0 ? '京东价' : '实付价'}</FormItem>
        <FormItem label="订单拆单后是否合并">{formData.split ? '是' : '否'}</FormItem>
        <FormItem label="订单商品">
          {formData.isAllOrderSku === 1 && (
            <div>
              全部商品<span style={{ fontSize: '12px', color: 'gray', marginTop: '15px' }}>(不含虚拟商品)</span>
            </div>
          )}
          {formData.isAllOrderSku === 0 && (
            <div>
              指定商品
              <SkuList skuList={formData.orderSkuList} />
            </div>
          )}
          {formData.isAllOrderSku === 2 && (
            <div>
              排除商品
              <SkuList skuList={formData.orderSkuList} />
            </div>
          )}
        </FormItem>
        <FormItem label="新老客限制">
          {formData.customerLimit === 0 && <div>不限制</div>}
          {formData.customerLimit === 1 && (
            <div>
              <div>仅限新客参与（近{formData.customerLimitDay}）天未购买</div>
            </div>
          )}
          {formData.customerLimit === 2 && (
            <div>
              <div>仅限老客参与（近{formData.customerLimitDay}）天已购买</div>
            </div>
          )}
        </FormItem>
        <FormItem label="设置阶梯奖励">
          <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
            <Table.Column title="阶梯等级" cell={(v, index) => <div>{index + 1}级</div>} />
            <Table.Column
              title="金额"
              cell={(v, index, row) => <div>累计金额{row.stepAmount}元</div>}
              dataIndex="stepAmount"
            />
            <Table.Column title="奖品名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(v, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
              dataIndex="prizeType"
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />

            <Table.Column
              title="单位价值(元)"
              cell={(v, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="发放份数"
              cell={(v, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="奖品图"
              cell={(v, index, row) => <img style={{ width: '20px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="领取限制">{formData.receiveLimit === 1 ? '单次领取' : '多次领取'}</FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
