/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-06 14:58
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import { Form, Field, Radio, DatePicker2, Table, Button, Dialog, NumberPicker, Input, Grid } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {activityEditDisabled, getShopOrderStartTime, getShopSelfPayPrice, isPopShop} from '@/utils';
import { FormLayout, PageData, PRIZE_INFO, PRIZE_TYPE } from '../../../util';
import ChooseGoods from '@/components/ChooseGoods';
import styles from './index.module.scss';
import LzTipPanel from '@/components/LzTipPanel';
import format from '@/utils/format';
import constant from '@/utils/constant';
import dayjs from 'dayjs';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import LzToolTip from '@/components/LzToolTip';
import SkuList from '@/components/SkuList';
import { getShop } from '@/utils/shopUtil';
import { getNotExcludedSku } from '@/api/sku';

const { RangePicker } = DatePicker2;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const { shopId } = getShop();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();

  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 选择奖品
  const [visible, setVisible] = useState(false);

  const [plan, setPlan] = useState<any[]>([]);
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const handleSkuChange = (data) => {
    setData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };
  const removeSku = (skuId: number) => (): void => {
    const index = formData.orderSkuList.findIndex((item) => item.skuId === skuId);
    formData.orderSkuList.splice(index, 1);
    setData({ orderSkuList: formData.orderSkuList });
  };
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: 0,
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRangeData');
    }, 1000);
  }, [formData.endTime]);
  const [maxDays, setMaxDays] = React.useState(180);
  const [firstIn, setFirstIn] = React.useState(false);
  useEffect(() => {
    if (!firstIn) {
      setFirstIn(true);
      return;
    }
    let diff = 180;
    if (shopOrderInfo.longTermOrder) {
      diff = dayjs(formData.endTime).diff(dayjs(shopOrderInfo.shopOrderStartTime), 'day');
    } else {
      diff = shopOrderInfo.orderRetentionDays;
    }
    setMaxDays(diff);
    if (diff < formData.days) {
      setData({ days: diff });
    }
  }, [formData.startTime, shopOrderInfo.shopOrderStartTime]);
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRangeData): void => {
    setData({
      orderRangeData,
      orderStartTime: format.formatDateTimeDayjs(orderRangeData[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRangeData[1]),
    });
  };
  const addPrize = () => {
    formData.prizeList.push(PRIZE_INFO);
    setData({ prizeList: formData.prizeList });
    field.setErrors({ prizeList: '' });
  };

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.prizeList[target] = { ...formData.prizeList[target], ...data };
    const list = formData.prizeList.map((item) => item.prizeKey);
    setPlan(list);
    setData(formData);
    setVisible(false);
  };

  useEffect(() => {
    // 根据奖品列表长度判断是否设置过奖品
    const prizeLength = formData.prizeList.length;
    if (!prizeLength) {
      const prizeList = new Array(2).fill(PRIZE_INFO);
      setData({ prizeList });
    }
  }, []);

  const validateOrderTime = (rule, val, callback): void => {
    // const { orderRestrainStartTime, orderRestrainEndTime } = formData;
    const orderRestrainStartTime = val[0];
    const orderRestrainEndTime = val[1];
    if (!orderRestrainStartTime || !orderRestrainEndTime) {
      callback('请选下单时间');
    } else if (
      !shopOrderInfo.longTermOrder &&
      dayjs(orderRestrainStartTime).valueOf() < shopOrderInfo.shopOrderStartTime
    ) {
      callback(`下单时间不能早于活动结束时间前${shopOrderInfo.orderRetentionDays}天`);
    } else if (dayjs(orderRestrainEndTime).startOf('s').isAfter(dayjs(formData.endTime))) {
      callback('下单时间不能晚于活动结束时间');
    } else {
      callback();
    }
  };

  // 是否有实付价能力
  const [hasSelfPayPrice, setHasSelfPayPrice] = useState(false);
  useEffect(() => {
    if (isPopShop()) {
      setHasSelfPayPrice(true);
    }
  }, []);

  const renderStepAmount = (v, index, record) => {
    const clonedPrizeList = JSON.parse(JSON.stringify(formData.prizeList));
    return (
      <div>
        累计金额（元）
        <Form.Item name={`prize-${index}`} style={{ marginTop: '5px' }} required requiredMessage="请填写累计金额">
          <NumberPicker
            disabled={activityEditDisabled()}
            style={{ margin: '0 5px' }}
            value={record.stepAmount}
            onChange={(stepAmount) => {
              clonedPrizeList[index].stepAmount = stepAmount;
              setData({ prizeList: clonedPrizeList });
            }}
            type="inline"
            min={1}
            precision={2}
            size={'small'}
            step={1}
            name={`prize-${index}`}
            max={9999999}
            className={styles.number}
          />
        </Form.Item>
      </div>
    );
  };
  const handlePreview = async (data) => {
    if (formData.isAllOrderSku === 0) {
      setData({ orderSkuListPreview: data });
    }
    if (formData.isAllOrderSku === 2 && data.length) {
      const skuIdsList = formData.orderSkuList.map((item) => item.skuId);
      // 调接口查排除商品
      await getNotExcludedSku({ skuIds: skuIdsList })
        .then((res: any): void => {
          console.log(res);
          setData({ orderSkuListPreview: res });
        })
        .catch((e) => {
          console.log(e.message);
        });
    }
  };
  return (
    <div>
      <LzPanel title="参与规则">
        <LzTipPanel message="提示：用户订单满足以下条件后获得领奖资格，领取奖励后，待订单完成，系统将自动发放奖励；领取上限为1份/人" />
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem
            label="下单时间"
            disabled={false}
            required
            requiredMessage="请选择下单时间"
            validator={validateOrderTime}
          >
            <RangePicker
              className="w-300"
              name="orderRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.orderRangeData}
              onChange={onDataRangeChange}
              disabled={[activityEditDisabled(), false]}
              disabledDate={(date) => {
                return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
              }}
            />
            <div className={styles.tip}>
              注：1、默认支持查询
              {shopOrderInfo.longTermOrder
                ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
              的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
              <br />
              2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。
            </div>
          </FormItem>
          <FormItem label="订单拆单后是否合并" required>
            <RadioGroup
              value={formData.split}
              onChange={(split: number) => {
                setData({ split });
              }}
            >
              <Radio id="1" value={1}>
                是
              </Radio>
              <Radio id="0" value={0}>
                否
              </Radio>
            </RadioGroup>
            <LzToolTip
              content={
                <div>
                  <div>
                    <p>拆单：因平台因素，用户的某笔总的父订单会有被拆单成多笔子订单发货的情况。</p>
                    <br />
                    当用户订单在平台上被拆分为多笔子订单时，可以选择以下方式来判断活动参与门槛： <br />
                    • 选择“是”：按照父订单（将本店铺的子订单合并后的订单）来判断参与门槛。 <br />
                    • 选择“否”：按照拆单后的子订单（包括父订单）来判断参与门槛。 <br />
                  </div>
                </div>
              }
            />
          </FormItem>
          <FormItem label="价格类型" required>
            <RadioGroup
              value={formData.moneyType}
              onChange={(moneyType: number) => {
                setData({ moneyType });
              }}
            >
              <Radio id="0" value={0}>
                京东价
                <LzToolTip
                  content={
                    <div>
                      <div>选择【全部商品】时，使用订单金额计算，即整笔订单京东价；</div>
                      <div>选择【指定商品】时，使用活动指定商品的 SKU京东价 × 购买数量 计算；</div>
                      <div> SKU京东价，为京东后台创建商品时所设置的SKU京东价；</div>
                    </div>
                  }
                />
              </Radio>
              <Radio value={1} disabled={!hasSelfPayPrice}>
                实付价
                <LzToolTip
                  content={
                    <div>
                      <div>注意：</div>
                      <div>
                        1.
                        背景：由于平台侧只提供POP店铺整笔订单的实付价金额，并不提供此订单中单个商品（例：整笔订单中包含多个商品）的实付价金额；
                      </div>
                      <div>
                        2. 影响：若使用拆单能力且选择实付价【云鹿】将通过整笔订单的折扣力度（订单实付价金额 /
                        订单京东价金额）来计算，该笔订单中单个商品的实付价金额；此方式计算的单个商品实付价金额将会与消费者实际的实付价金额有一定的出入，望知悉。若不使用拆单能力实付价则为京东下发订单的实付价为准；
                      </div>
                      <div>
                        3. 另：因为平台并不提供该笔订单的运费数据（有的店铺需要额外支付运费），因此有极小概率会出现：
                        【云鹿】计算的实付价金额 大于 京东价；
                      </div>
                      <div>4. 请您谨慎选择价格类型。</div>
                    </div>
                  }
                />
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="订单商品" required>
            <RadioGroup
              value={formData.isAllOrderSku}
              onChange={(isAllOrderSku: number) => {
                formData.orderSkuListPreview = [];
                setData({
                  isAllOrderSku,
                  orderSkuList: [],
                });
              }}
            >
              <Radio id="1" value={1}>
                全部商品<span className={styles.tip}>(不含虚拟商品)</span>
              </Radio>
              <Radio id="0" value={0}>
                指定商品
                <LzToolTip content="商家需配置指定SKU为参与活动商品。满足参与资格的用户需在下单时间段内下单指定商品，满足XX元，且订单已完成，才有机会获得奖励；需注意：计算金额时，使用活动指定商品的SKU京东价×购买数量计算，而不是包含活动指定商品的整笔订单金额计算" />
              </Radio>
              <Radio id="2" value={2}>
                排除商品
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {(formData.isAllOrderSku === 0 || formData.isAllOrderSku === 2) && (
                <FormItem
                  name="orderSkuList"
                  required
                  requiredMessage={'请选择订单商品'}
                  style={{ marginTop: '15px' }}
                  disabled={false}
                >
                  <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList.length ? 1 : ''} />
                  <ChooseGoods
                    max={formData.isAllOrderSku === 2 ? 100 : 0}
                    defaultValue={defaultValue.orderSkuList}
                    value={formData.orderSkuList}
                    onChange={handleSkuChange}
                    isExcept={formData.isAllOrderSku === 2}
                  />
                  <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={removeSku} />
                  <p className={styles.tip}>注：商品价格每天凌晨同步;</p>
                </FormItem>
              )}
            </Grid.Row>
            <LzTipPanel
              message={
                <div>
                  选择【全部商品】时，使用订单金额计算，即整笔订单京东价;
                  <br />
                  选择【指定商品】或【排除商品】时，若使用拆单能力 指定商品的订单价格计算为使用活动指定商品的 SKU京东价
                  × 购买数量*(订单实付价金额 / 订单京东价金额)计算。
                  <br />
                  若不使用拆单能力使用活动指定商品的 SKU京东价 × 购买数量 计算
                  <br />
                  SKU京东价，为京东后台创建商品时所设置的SKU京东价
                </div>
              }
            />
          </FormItem>
          <FormItem label="新老客限制" required>
            <RadioGroup
              value={formData.customerLimit}
              onChange={(customerLimit: number) => {
                field.setErrors({ customerLimitDay: '' });
                setData({ customerLimit, customerLimitDay: 1 });
              }}
            >
              <Radio id="0" value={0}>
                不限制
              </Radio>
              <Radio id="1" value={1}>
                仅限新客参与
                <LzToolTip content="输入X天前到下单开始时间段内不存在订单完成状态的用户。" />
              </Radio>
              <Radio id="2" value={2}>
                仅限老客参与
                <LzToolTip content="输入X天前到下单开始时间段内存在订单完成状态的用户。" />
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {formData.customerLimit === 1 && (
                <FormItem
                  name="customerLimitDay"
                  style={{ marginTop: 10 }}
                  required
                  requiredMessage={'请输入新客限制天数'}
                >
                  近
                  <NumberPicker
                    min={1}
                    max={maxDays}
                    type="inline"
                    name="customerLimitDay"
                    style={{ margin: '0 5px' }}
                    value={formData.customerLimitDay}
                    onChange={(customerLimitDay) => {
                      setData({ customerLimitDay });
                    }}
                  />
                  天内未购买
                </FormItem>
              )}
            </Grid.Row>

            <Grid.Row>
              {formData.customerLimit === 2 && (
                <FormItem
                  style={{ marginTop: 10 }}
                  name="customerLimitDay"
                  required
                  requiredMessage={'请输入老客限制天数'}
                >
                  近
                  <NumberPicker
                    min={1}
                    max={maxDays}
                    type="inline"
                    name="customerLimitDay"
                    style={{ margin: '0 5px' }}
                    value={formData.customerLimitDay}
                    onChange={(customerLimitDay) => {
                      setData({ customerLimitDay });
                    }}
                  />
                  天内已购
                </FormItem>
              )}
            </Grid.Row>
          </FormItem>
          <FormItem label="设置阶梯奖励" required requiredMessage={'请设置阶梯奖励'}>
            <Button disabled={formData.prizeList.length >= 5} onClick={addPrize}>
              添加
            </Button>
            <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
              <Table.Column title="阶梯等级" cell={(v, index) => <div>{index + 1}级</div>} />
              <Table.Column title="金额" cell={renderStepAmount} dataIndex="stepAmount" />
              <Table.Column title="奖品名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(v, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="单位价值(元)"
                cell={(v, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="发放份数"
                cell={(v, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(v, index, row) => <img style={{ width: '20px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, r) => (
                  <div>
                    {(!activityEditDisabled() || (activityEditDisabled() && r.prizeName !== '谢谢参与')) && (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.prizeList[index];
                          if (row.prizeName === '谢谢参与') {
                            row = null;
                          }
                          setEditValue(row);
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                    )}
                    {!activityEditDisabled() && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空阶梯奖励？',
                              onOk: () => {
                                formData.prizeList.splice(index, 1, PRIZE_INFO);
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          清空
                        </Button>
                        <Button
                          text
                          type="primary"
                          disabled={formData.prizeList.length <= 2}
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该阶梯奖励？',
                              onOk: () => {
                                formData.prizeList.splice(index, 1);
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          删除
                        </Button>
                      </>
                    )}
                  </div>
                )}
              />
            </Table>
            <Input className="validateInput" name="prizeList" value={formData.prizeList.length ? 1 : ''} />
          </FormItem>
          <FormItem label="领取限制" required>
            <RadioGroup
              value={formData.receiveLimit}
              onChange={(receiveLimit: number) => {
                setData({ receiveLimit });
              }}
            >
              <Radio id="1" value={1}>
                单次领取
                <LzToolTip content="单次领取，指用户满足多个阶梯层级时，只能选择一个阶梯层级领取奖励，不可领取全部奖励" />
              </Radio>
              <Radio id="2" value={2}>
                多次领取
                <LzToolTip content="多次领取，指用户满足多个阶梯层级时，可以领取全部奖励" />
              </Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </LzPanel>
      <LzDialog
        title={false}
        style={{ width: '700px' }}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
      >
        <ChoosePrize
          formData={formData}
          hasProbability={false}
          hasLimit={false}
          editValue={editValue}
          onChange={onPrizeChange}
          planList={plan}
          typeList={[1, 2, 3, 4, 6, 7, 8, 9, 10, 12, 15]}
          onCancel={() => setVisible(false)}
          defaultPrize={defaultValue.prizeList[target]}
        />
      </LzDialog>
    </div>
  );
};
