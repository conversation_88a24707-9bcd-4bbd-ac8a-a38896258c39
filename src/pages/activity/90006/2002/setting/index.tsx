import React, { useImperative<PERSON><PERSON><PERSON>, useReducer, useRef, useState } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 抽奖机会
import ChanceInfo from './components/Chance';
// 奖品信息
import PrizesInfo from './components/Prizes';
// 曝光商品
import Exposure from './components/Exposure';
// 分享设置
import ShareInfo from './components/Share';
// 活动规则
import RulesInfo from './components/Rules';
import { CustomValue, PageData } from '@/pages/activity/90006/2002/util';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}

interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [renderBase] = useState(true);
  const [renderPrize, setRenderPrize] = useState(formData.threshold !== 1);

  const onChange = (activityInfo, type?: string): void => {
    console.log('活动设置信息更新:', activityInfo);
    if (activityInfo.supportLevels) {
      setTimeout(() => {
        setRenderPrize(true);
      });
    }
    if (activityInfo.childList.length) {
      activityInfo.rangeDate = [
        new Date(activityInfo.childList[0].promotionStartTime),
        new Date(activityInfo.childList[activityInfo.childList.length - 1].promotionEndTime),
      ];
      activityInfo.startTime = activityInfo.childList[0].promotionStartTime;
      activityInfo.endTime = activityInfo.childList[activityInfo.childList.length - 1].promotionEndTime;
    }
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const chanceRef = useRef<{ submit: () => void | null }>(null);
  const shareRef = useRef<{ submit: () => void | null }>(null);
  const exposureRef = useRef<{ submit: () => void | null }>(null);
  const rulesRef = useRef<{ submit: () => void | null }>(null);
  const prizesRef = useRef<{ submit: () => void | null }>(null);

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: any[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current!,
        chanceRef.current,
        exposureRef.current,
        shareRef.current,
        prizesRef.current,
        rulesRef.current,
      ];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        const result = events[index].submit();
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  return (
    <div className={styles.setting}>
      {renderBase && <BaseInfo sRef={baseRef} {...settingProps} />}
      <ChanceInfo sRef={chanceRef} {...settingProps} />
      {renderPrize && <PrizesInfo sRef={prizesRef} {...settingProps} />}
      <Exposure sRef={exposureRef} {...settingProps} />
      <ShareInfo sRef={shareRef} {...settingProps} decoValue={decoValue} />
      <RulesInfo sRef={rulesRef} {...settingProps} />
    </div>
  );
};
