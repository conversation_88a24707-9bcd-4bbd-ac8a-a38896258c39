/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useEffect, useImperativeHandle, useReducer } from 'react';
import { Field, Form } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled } from '@/utils';

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="虚拟商品设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item label="预定所需金额" required isPreview>
            {/* <Row gutter="4"> */}
            {/*   <Col> */}
            {/*     <Form.Item */}
            {/*       name="reservationAmountShow" */}
            {/*       required */}
            {/*       requiredTrigger="onBlur" */}
            {/*       requiredMessage="请输入每人每天最多中奖次数" */}
            {/*       style={{ margin: 0 }} */}
            {/*     > */}
            {/*       <NumberPicker */}
            {/*         value={formData.reservationAmountShow} */}
            {/*         onChange={(reservationAmountShow: number) => setData({ reservationAmountShow })} */}
            {/*         className={styles.number} */}
            {/*         type="inline" */}
            {/*         min={0.01} */}
            {/*         max={9999999} */}
            {/*       /> */}
            {/*       元 */}
            {/*     </Form.Item> */}
            {/*   </Col> */}
            {/* </Row> */}
            <div>{formData.reservationAmountShow} 元</div>
          </Form.Item>
          <Form.Item label="预定限制" isPreview>
            <div>每个用户仅可预定一次，支付成功代表已预定</div>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
