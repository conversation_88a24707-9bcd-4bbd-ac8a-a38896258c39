/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, { useEffect, useImperativeHandle, useReducer } from 'react';
import { Button, Field, Form, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {
  checkActivityData,
  ChildInfo,
  FormLayout,
  generateMembershipString,
  PageData,
} from '@/pages/activity/90006/2002/util';
import format from '@/utils/format';
import { getShop } from '@/utils/shopUtil';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  // 生成促销活动规则
  const numToChinese = (num: number): string => {
    const numCh = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
    return numCh[num];
  };
  const generatePromotionRule = (): string => {
    const { childList } = formData;
    let ruleStr = '';
    childList.forEach((item: ChildInfo, index: number) => {
      ruleStr += `（${item.num}）第${numToChinese(item.num)}期促销活动：累计最大参与人数
`;
    });
    return ruleStr;
  };

  /**
   * 自动生成规则说明
   */
  const autoCreateRuleDesc = async (): Promise<void> => {
    // // 判断奖品列表是否存在有效奖品
    const { childList } = formData;
    const isValidateData: boolean = checkActivityData(childList, formData);
    if (!isValidateData) {
      return;
    }
    const rules = `1. 活动时间：${format.formatDateTimeDayjs(formData.startTime)}~${format.formatDateTimeDayjs(
      formData.endTime,
    )}

2. 活动门槛：${generateMembershipString(formData, 'string')}；

3. 活动参与方式：
第一步：符合活动门槛的用户，支付0.01元，支付成功即可预定全期促销活动的加赠礼权益；
第二步：在每期促销活动时间内，已支付成功的用户在店内加购指定正装商品（以活动页面显示结果为准）；
第三步：在购物车，勾选待购买的正装商品并点击“换促销”或者“去换购”或者“重新领取”，领取加赠礼再去结算！（若用户未在购物车勾选赠品，后续不予补发）；
第四步：成功付款正装商品后，礼品将随正装商品订单发出；

4. ${formData.reservationAmountShow}元用于预定全期加赠礼福利，每个ID限购一次，一经购买，不支持退款；

5. ${
      formData.reservationAmountShow
    }元支付成功后，加赠礼领取资格预计10分钟内发放，若在购物车未看到赠品，请确认加购的正装商品是否为活动指定商品，是否达到满赠门槛，或耐心等待稍后再试；如确认${
      formData.reservationAmountShow
    }元支付成功后，未获取到赠品领取资格，可凭借活动页面展示的领取记录截图联系客服补领取；

6. ${
      formData.reservationAmountShow
    }元支付成功后请及时在促销时间内下单，逾期未下单或未付款指定正装商品或下单后退款包括收到商品后退货，视为放弃赠品福利，退单后再次下单将不会再有赠品附送；

7. 参与促销活动的指定正装商品，在活动页面会展示；

8. 每期促销活动的赠品数量有限，每个ID限领1份，先到先得，送完即止；
${generatePromotionRule()}
9. 若正装商品退货，赠品需一并退回；

10.活动高峰期间订单校验会有所延迟，请耐心等待稍后再试；

11. 凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其获奖资格（如奖品已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利；

12. 此活动最终解释权归${getShop().shopName}所有`;
    setData({ rules });
    field.setErrors({ rules: '' });
  };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请生成/输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <Button
              type="primary"
              className="table-cell-btn"
              onClick={autoCreateRuleDesc}
              style={{ marginRight: '15px' }}
              text
            >
              自动生成规则说明
            </Button>
            <span style={{ color: 'red', fontSize: '12px' }}>
              提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。
            </span>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
