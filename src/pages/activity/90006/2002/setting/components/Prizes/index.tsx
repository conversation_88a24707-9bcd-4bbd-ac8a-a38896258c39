/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useEffect, useImperativeHandle, useReducer, useState } from 'react';
import { Button, Dialog, Field, Form, Message, Table } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import Promotion from '@/pages/activity/90006/2002/setting/components/Promotion';
import ChoosePromotion from '@/components/ChoosePromotion';
import { activityEditDisabled, getParams } from '@/utils';
import { CHILD_INFO, FormLayout, PageData } from '../../../util';
import styles from '../../style.module.scss';
import dayjs from 'dayjs';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

// 数字转中文数字，例如12转成十二
const numToChinese = (num: number): string => {
  const numCh = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
  return numCh[num];
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState<any>(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // dialog title
  const [title, setTitle] = useState('添加促销活动');

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    if (data) {
      formData.prizeList[0] = data;
    } else {
      formData.prizeList = [];
    }
    setData(formData);
  };
  const onChildListChange = (data): boolean | void => {
    if (target) {
      if (dayjs(data.promotionStartTime).isBefore(formData.childList[target - 1].promotionEndTime)) {
        Message.error('本期促销活动开始时间不能早于上一期活动结束时间');
        return;
      }
    }
    if (target < formData.childList.length - 1) {
      if (dayjs(data.promotionEndTime).isAfter(formData.childList[target + 1].promotionStartTime)) {
        Message.error('本期促销活动结束时间不能晚于下一期活动开始时间');
        return;
      }
    }
    // 促销活动时间应在令牌有效期内
    if (formData.prizeList.length) {
      if (
        dayjs(data.promotionStartTime).isBefore(formData.prizeList[0].beginTime) ||
        dayjs(data.promotionEndTime).isAfter(formData.prizeList[0].endTime)
      ) {
        Message.error('促销活动时间应在令牌有效期内');
        return;
      }
    }
    // 更新指定index 奖品信息
    formData.childList[target] = data;
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // useEffect((): void => {
  //   // 初始奖品列表长度
  //   const childListLength = formData.childList.length;
  //   // 生成默认奖品列表
  //   const list: ChildInfo[] = [...formData.childList];
  //   // 补齐奖品数到8
  //   for (let i = 0; i < 8 - childListLength; i++) {
  //     list.push(CHILD_INFO);
  //   }
  //   // 如果奖品列表为空 说明：奖品列表已经够8 直接用childList
  //   // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
  //   setData({ childList: list.length ? list : formData.childList });
  // }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel
        title="奖品设置"
        actions={
          <Button
            type="primary"
            text
            onClick={() => {
              window.open('https://www.yuque.com/luzekeji/bfc3uk/zm9hot5lmex11zc2');
            }}
          >
            令牌使用攻略
          </Button>
        }
      >
        <Form {...formItemLayout} field={field}>
          <FormItem isPreview label="奖品类型">
            总价促销令牌（满赠）
          </FormItem>
          <FormItem label="添加令牌" required>
            <ChoosePromotion
              promoType={'10'}
              submit={onPrizeChange}
              value={formData.prizeList[0] ?? null}
              disabled={activityEditDisabled() || getParams('type') === 'edit'}
            />
          </FormItem>
          <Form.Item label="发放时机" isPreview>
            <div>
              用户支付{formData.reservationAmountShow}元成功后，系统自动发放总价促销令牌（即系统自动将用户加入令牌中）
            </div>
          </Form.Item>
          <FormItem label="令牌中绑定的促销活动" required>
            <div className={styles.topTip}>
              1.请按照促销活动开始时间由早至晚依次添加；多个促销活动时间不可重叠；随着支付0.01元的用户数量逐期增大，累计参与人数应递增
              <br />
              2.若有多期促销活动，活动时间<span style={{ color: 'red' }}>不建议无缝连续</span>
              ，原因为：第一期活动结束后会立即移除所有一期内的用户令牌加入到第二期，如果人数较多，加入时间过长，
              在没有加入完毕的情况下，若有用户下单，无法领取赠品，将面临客诉风险。
            </div>
            <Table dataSource={formData.childList} style={{ marginTop: '15px' }}>
              <Table.Column title="期次" cell={(_, index, row) => <div>第{numToChinese(row.num)}期</div>} />
              <Table.Column
                title="促销活动时间"
                cell={(_, index, row) => (
                  <div>
                    起：{row.promotionStartTime}
                    <br />
                    止：{row.promotionEndTime}
                  </div>
                )}
                dataIndex="prizeType"
              />
              {activityEditDisabled() && (
                <Table.Column
                  title="促销活动状态"
                  cell={(_, index, row) => (
                    // 促销活动开始时间小于当前时间为未开始，结束时间大于当前时间为已结束，否则为进行中
                    <div>
                      {row.promotionStartTime < dayjs().format('YYYY-MM-DD HH:mm:ss') &&
                      row.promotionEndTime > dayjs().format('YYYY-MM-DD HH:mm:ss') ? (
                        <span style={{ color: 'green' }}>进行中</span>
                      ) : row.promotionStartTime > dayjs().format('YYYY-MM-DD HH:mm:ss') ? (
                        <span style={{ color: 'gray' }}>未开始</span>
                      ) : (
                        <span style={{ color: 'red' }}>已结束</span>
                      )}
                    </div>
                  )}
                />
              )}
              <Table.Column
                title="赠品海报"
                cell={(_, index, row) => <img style={{ width: '150px' }} src={row.giftPoster} alt="" />}
              />
              <Table.Column
                title="赠品Sku"
                cell={(_, index, row) => (
                  <div>
                    {row.giftSkuList.map((item) => (
                      <span key={item.skuId}>
                        {item.skuId}
                        <br />
                      </span>
                    ))}
                  </div>
                )}
              />
              <Table.Column
                title="操作"
                width={100}
                cell={(val, index, _) => (
                  <div>
                    {activityEditDisabled() &&
                      formData.childList[index].promotionEndTime < dayjs().format('YYYY-MM-DD HH:mm:ss') && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            setEditValue(formData.childList[index]);
                            setTarget(index);
                            setTitle('查看促销活动');
                            setVisible(true);
                          }}
                        >
                          查看
                        </Button>
                      )}
                    {(!activityEditDisabled() ||
                      formData.childList[index].promotionEndTime > dayjs().format('YYYY-MM-DD HH:mm:ss')) && (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          setEditValue(formData.childList[index]);
                          setTarget(index);
                          setTitle('编辑促销活动');
                          setVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                    )}
                    {!activityEditDisabled() && (
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          Dialog.confirm({
                            v2: true,
                            title: '提示',
                            centered: true,
                            content: '确认删除该促销活动？',
                            onOk: () => {
                              // 从数组中删除该条数据，不需要重置
                              formData.childList.splice(index, 1);
                              formData.childList.forEach((e, i) => {
                                e.num = i + 1;
                              });
                              setData(formData);
                            },
                            onCancel: () => console.log('cancel'),
                          } as any);
                        }}
                      >
                        删除
                      </Button>
                    )}
                  </div>
                )}
              />
            </Table>
            {!activityEditDisabled() && formData.childList.length < 2 && (
              <Button
                type="secondary"
                onClick={() => {
                  setEditValue({
                    ...CHILD_INFO,
                    num: formData.childList.length + 1,
                  });
                  setTarget(formData.childList.length);
                  setTitle('添加促销活动');
                  setVisible(true);
                }}
                className={styles.addButton}
              >
                添加促销活动({formData.childList.length}/2)
              </Button>
            )}
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={title}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '820px' }}
      >
        <Promotion editValue={editValue} onChange={onChildListChange} onCancel={onCancel} />
      </LzDialog>
    </div>
  );
};
