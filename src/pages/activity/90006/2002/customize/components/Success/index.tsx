import React, { useEffect, useReducer } from 'react';
import styles from './style.module.scss';
import { Button, Form, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel
        title="预定成功页"
        subTitle="此页面为用户支付0.01元成功后，展示当前促销活动的加赠礼及可购买的正装商品"
        actions={
          <Button
            type="primary"
            text
            onClick={() => {
              window.location.href =
                'https://bigitem.oss-cn-zhangjiakou.aliyuncs.com/crm/sass/psd/0.01%E5%85%83%E9%94%81%E6%9D%83-%E5%8A%A0%E8%B4%AD%E6%AD%A3%E8%A3%85%E8%B5%A0%E7%89%B9%E6%9D%83.psd';
            }}
          >
            下载psd素材包
          </Button>
        }
      >
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="页面主图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.successKvImg}
                  onChange={(successKvImg) => {
                    setForm({ successKvImg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、推荐图片高度480/790/1334px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ successKvImg: defaultValue?.successKvImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="页面背景颜色">
            <LzColorPicker value={formData.successBgColor} onChange={(successBgColor) => setForm({ successBgColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ successBgColor: defaultValue?.successBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
