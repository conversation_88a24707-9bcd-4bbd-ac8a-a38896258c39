/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 设置模板
 */
import React, { useState } from 'react';
import { CustomValue } from '../util';
// 基础信息
import Base from './components/Base';
// 攻略弹窗
import Strategy from './components/Strategy';
import { Tab } from '@alifd/next';
import Prize from './components/Prize';
import Step from './components/Step';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { target, defaultValue, value, handleChange } = props;
  console.log('target', target);
  const [activeKey, setActiveKey] = useState('1');
  const handleTabChange = (data) => {
    setActiveKey(data);
  };
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab activeKey={activeKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Tab.Item title="活动主页" key="1" />
        <Tab.Item title="攻略弹窗" key="2" />
      </Tab>
      {activeKey === '1' && (
        <>
          {<Base {...eventProps} />}
          {<Prize {...eventProps} />}
          {<Step {...eventProps} />}
        </>
      )}
      {activeKey === '2' && <Strategy {...eventProps} />}
    </div>
  );
};
