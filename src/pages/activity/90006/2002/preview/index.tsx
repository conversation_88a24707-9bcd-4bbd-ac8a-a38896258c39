/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Input, Table } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData } from '../util';
import styles from './style.module.scss';
import format from '@/utils/format';
import ChoosePromotion from '@/components/ChoosePromotion';
import dayjs from 'dayjs';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

// 数字转中文数字，例如12转成十二
const numToChinese = (num: number): string => {
  const numCh = ['零', '一', '二'];
  return numCh[num];
};

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="预定所需金额">{formData.reservationAmountShow}元</FormItem>
        <FormItem label="预定限制">每个用户仅可预定一次，支付成功代表已预定所有期加赠礼权益</FormItem>
        <FormItem label="奖品类型">总价促销令牌（满赠）</FormItem>
        <FormItem label="令牌">
          <ChoosePromotion value={formData.prizeList[0] ?? null} disabled />
        </FormItem>
        <FormItem label="奖品列表" isPreview={false}>
          <Table dataSource={formData.childList} style={{ marginTop: '15px' }}>
            <Table.Column title="期次" cell={(_, index, row) => <div>第{numToChinese(row.num)}期</div>} />
            <Table.Column
              title="促销活动时间"
              cell={(_, index, row) => (
                <div>
                  起：{row.promotionStartTime}
                  <br />
                  止：{row.promotionEndTime}
                </div>
              )}
              dataIndex="prizeType"
            />
            {(window.location.href.includes('edit') || window.location.href.includes('activityManage')) && (
              <Table.Column
                title="促销活动状态"
                cell={(_, index, row) => (
                  // 促销活动开始时间小于当前时间为未开始，结束时间大于当前时间为已结束，否则为进行中
                  <div>
                    {row.promotionStartTime < dayjs().format('YYYY-MM-DD HH:mm:ss') &&
                    row.promotionEndTime > dayjs().format('YYYY-MM-DD HH:mm:ss') ? (
                      <span style={{ color: 'green' }}>进行中</span>
                    ) : row.promotionStartTime > dayjs().format('YYYY-MM-DD HH:mm:ss') ? (
                      <span style={{ color: 'gray' }}>未开始</span>
                    ) : (
                      <span style={{ color: 'red' }}>已结束</span>
                    )}
                  </div>
                )}
              />
            )}
            <Table.Column
              title="赠品海报"
              cell={(_, index, row) => <img style={{ width: '150px' }} src={row.giftPoster} alt="" />}
            />
            <Table.Column
              title="赠品SKU"
              cell={(_, index, row) => (
                <div>
                  {row.giftSkuList.map((sku) => (
                    <div key={sku.skuId}>
                      {sku.skuId}
                      <br />
                    </div>
                  ))}
                </div>
              )}
            />
          </Table>
        </FormItem>
        <FormItem label="是否添加曝光商品">
          {formData.isExposure === 0 && <div>否</div>}
          {formData.isExposure === 1 && <SkuList skuList={formData.skuList} />}
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
