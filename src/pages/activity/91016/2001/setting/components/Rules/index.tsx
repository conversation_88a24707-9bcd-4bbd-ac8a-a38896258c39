import React, { useImperative<PERSON>andle, useReducer, useEffect } from 'react';
import { Form, Button, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, generateMembershipString, PageData, checkActivityData } from '../../../util';
import format from '@/utils/format';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  const getRule3 = () => {
    const rules: any = [];
    formData.taskRequestList.forEach((item, index) => {
      rules.push(
        `（${index + 1}） ${item.prizeList[0].prizeName}: 共${item.prizeList[0].sendTotalCount}份，单份奖品价值${
          item.prizeList[0].unitPrice
        }元；`,
      );
    });
    return rules.join('\n');
  };
  /**
   * 自动生成规则说明
   */
  const autoCreateRuleDesc = async (): Promise<void> => {
    const isValidateData: boolean = checkActivityData(formData);
    if (!isValidateData) {
      return;
    }
    const rules = `1.活动时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
      formData.endTime,
    )}；
2.活动对象：${generateMembershipString(formData, 'string')}
3.奖品说明：
${getRule3()}
4.参与规则
（1）在${format.formatDateTimeDayjs(formData.orderStartTime[0])}至${format.formatDateTimeDayjs(
      formData.orderStartTime[1],
    )}内有下单完成记录即可参与；
（2）${formData.assignGoodsFlag === 0 ? '参与活动商品本店全部商品；' : '参与活动商品部分指定商品；'}${
      formData.configurationType === 2 && formData.fullTimesMoney > 0
        ? `
（3）单笔订单金额需大于${formData.fullTimesMoney}元${
            formData.priceType === 1
              ? '，订单商品计算金额为实付价金额，订单存在延迟，需要在订单完成后T+1.75天可参与活动'
              : ''
          }；`
        : ''
    }
 （注意：如果参与活动商品是预售商品，需要订单支付尾款且满足活动设置规则才可以参与活动。）
5.${
      formData.taskRequestList.length === 1 || (formData.taskRequestList.length > 1 && formData.prizeMethod === 1)
        ? '若此用户满足多个门槛可领取对应门槛的奖励，每个门槛一个ID仅能领取一次；'
        : '若此用户满足多个门槛，可自由选择一个门槛的奖励，且一个ID仅能领取一次；'
    }
6.满足下单需求的会员需及时填写赠品收货信息；
7.满足条件参与领取后，待订单完成后${formData.delayPrizeFlag ? `${formData.delayPrizeDays}天` : ''}，才可领取奖励；
8.礼品数量有限，先到先得，有可能会有礼品擦肩而过情况；
9.领取后不代表获得奖品，奖品以实际发放情况为主；
10.因客户原因导致的恶意退款，取消赠品获取资格；
11.实物奖品需填写收货地址后进行发货；
12.活动高峰期间订单校验会有所延迟，请耐心等待稍后再试；
13.如果是实物奖项，请于1小时内填写地址，否则将视为放弃机会；
14.因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新；
`;
    setData({ rules });
    field.setErrors({ rules: '' });
  };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请生成/输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <Button
              type="primary"
              className="table-cell-btn"
              onClick={autoCreateRuleDesc}
              style={{ marginRight: '15px' }}
              text
            >
              自动生成规则说明
            </Button>
            <span style={{ color: 'red', fontSize: '12px' }}>
              提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。
            </span>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
