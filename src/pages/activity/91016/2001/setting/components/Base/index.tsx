import React, { useEffect, useImperativeHandle, useReducer } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Input } from '@alifd/next';
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import LzThreshold from '@/components/LzThreshold';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data: any): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate: any): void => {
    setData({
      rangeDate,
      orderStartTime: rangeDate,
      startTime: rangeDate[0] ? format.formatDateTimeDayjs(rangeDate[0]) : '',
      endTime: rangeDate[0] ? format.formatDateTimeDayjs(rangeDate[1]) : '',
    });
  };
  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    const data = rangeDate.filter((e) => e);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {data.length > 0 && (
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        )}
        {data.length <= 0 && (
          <span>
            活动持续
            <span style={{ color: 'red' }}>0</span>天
          </span>
        )}
      </span>
    );
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="活动基本信息">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="活动名称" required requiredMessage="请输入活动名称" disabled={false}>
            <Input
              value={formData.activityName}
              placeholder="请输入活动名称"
              name="activityName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(activityName) => setData({ activityName })}
            />
          </FormItem>
          <FormItem
            label="活动时间"
            required
            requiredMessage="请选择活动时间"
            extra={<div className="next-form-item-help">注：活动时间需设置在软件订购有效期内</div>}
          >
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              followTrigger
              format={dateFormat}
              hasClear={false}
              showTime
              value={
                [dayjs(formData.rangeDate[0]), dayjs(formData.rangeDate[1])] || [
                  new Date(formData.startTime),
                  new Date(formData.endTime),
                ]
              }
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <DateRangeDuration
              rangeDate={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
            />
          </FormItem>
          <FormItem label="活动门槛" required>
            <LzThreshold formData={formData} field={field} setData={setData} apply={[0, 1, 2]} applyGrade={[-9]} />
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
