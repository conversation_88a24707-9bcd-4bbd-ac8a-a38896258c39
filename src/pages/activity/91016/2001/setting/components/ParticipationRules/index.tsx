import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Field, Radio, DatePicker2, Icon, Balloon, Table, Button, NumberPicker, Grid, Input } from '@alifd/next';
import styles from '../../style.module.scss';
import { activityEditDisabled, getShopOrderStartTime, getShopSelfPayPrice, isDisableSetPrize } from '@/utils';
import { activityTypeGroup, PRIZE_TYPE, FormLayout, PageData } from '../../../util';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import ChooseGoods from '@/components/ChooseGoods';
import SkuList from '@/components/SkuList';
import { getShopId } from '@/utils/shopUtil';

const PRIZE_INFO = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
};
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 19,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  consumptionActivityType: any;
}

export default ({ onChange, defaultValue, value, sRef, consumptionActivityType = 0 }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [plan, setPlan] = useState<any[]>([]);
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: dayjs(formData.endTime).subtract(180, 'days').startOf('day').valueOf(),
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRestrainRangeData');
    }, 1000);
  }, [formData.endTime]);
  const addPrize = () => {
    formData.taskRequestList.push({
      prizeList: [{ ...PRIZE_INFO }],
      amount: 1,
      id: `id-${Math.random().toString(36).substr(2, 9)}`,
    });
    setData(formData);
  };
  const onCancel = (): void => {
    setVisible(false);
  };
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.taskRequestList[target] = {
      prizeList: [
        {
          ...data,
          prizeKey: data.planId || data.promoId || data.prizeKey,
          prizeName: data.prizeName || data.planName,
        },
      ],
      amount: formData.taskRequestList[target].amount,
    };
    const list = formData.taskRequestList.map((item) => item.prizeList[0].prizeKey);
    setPlan(list);
    setData(formData);
    setVisible(false);
  };
  const onAwardConditionsChange = (data, index): boolean | void => {
    // 更新指定index 奖品信息
    const newFormData = { ...formData };
    newFormData.taskRequestList[index].amount = data;
    setData(newFormData);
  };
  const handleSkuChange = (data) => {
    setData({ skuList: data });
    field.setErrors({ skuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    formData.skuList.splice(index, 1);
    setData({ skuList: formData.skuList });
  };
  const onDataRangeChange = (orderStartTime: any[]): void => {
    setData({
      orderStartTime,
    });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const handlePreview = (data) => {
    setData({ skuListPreview: data });
  };
  // 是否有实付价能力
  // const [hasSelfPayPrice, setHasSelfPayPrice] = useState(false);
  // useEffect(() => {
  //   getShopSelfPayPrice().then((res) => {
  //     setHasSelfPayPrice(res);
  //   });
  // }, []);
  return (
    <div>
      <LzPanel
        title={
          <div>
            参与规则{' '}
            <span className={styles['tips-bg']}>
              备注：买家需要在订单完成后（确认收货），才可以在活动页领取活动奖品
            </span>
          </div>
        }
      >
        <Form {...formItemLayout} field={field}>
          <FormItem label="下单时间" required requiredMessage="请选择下单并完成订单时间">
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              followTrigger
              format={dateFormat}
              hasClear={false}
              showTime
              defaultValue={[new Date(formData.rangeDate[0]), new Date(formData.rangeDate[1])]}
              value={[new Date(formData.orderStartTime[0]), new Date(formData.orderStartTime[1])]}
              onChange={onDataRangeChange}
              disabled={activityEditDisabled()}
              disabledDate={(date) => {
                return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
              }}
            />
            <Balloon.Tooltip
              style={{ minWidth: 940 }}
              v2
              trigger={<Icon type="help" size="small" className={styles['help-icon-style']} />}
              align="t"
            >
              注：1、默认支持查询
              {shopOrderInfo.longTermOrder
                ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
              的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理，结束时间不能晚于活动结束时间；
              <br />
              2，如设置订单完成时间早于活动的开始时间，则可能出现用户进入活动即可领奖的情况，请谨慎设置。
            </Balloon.Tooltip>
          </FormItem>
          <FormItem label="消费类型" required>
            <div className={styles['line-height']}>
              {activityTypeGroup[consumptionActivityType].consumptionActivityType}
            </div>
          </FormItem>
          {consumptionActivityType === 2 && (
            <FormItem label="单笔订单金额" required requiredMessage="请输入单笔订单金额">
              大于等于&nbsp;
              <NumberPicker
                name="fullTimesMoney"
                min={0}
                max={99999}
                precision={2}
                type="inline"
                value={formData.fullTimesMoney}
                onChange={(fullTimesMoney: number) => setData({ fullTimesMoney })}
                disabled={activityEditDisabled()}
              />
              &nbsp;元
            </FormItem>
          )}
          <FormItem label="奖励设置" required>
            <div className={styles['tips-box']}>
              <div className={styles['gray-text']} style={{ marginRight: '5px' }}>
                备注
              </div>
              <div>
                <div className={styles['gray-text']}>1，{activityTypeGroup[consumptionActivityType].tipsText}</div>
                <div className={styles['gray-text']}>2，同一门槛奖品只能领取一次；</div>
                <div className={styles['red-text']}>
                  3，阶梯式门槛最多可配置四个（根据店铺订单大数据推荐的最佳配置）
                </div>
              </div>
            </div>
            <Table dataSource={formData.taskRequestList} style={{ marginTop: 10 }}>
              <Table.Column
                title="领奖条件"
                cell={(_, index, row) => {
                  return (
                    <div>
                      累计消费
                      <NumberPicker
                        onChange={(v: number) => {
                          onAwardConditionsChange(v, index);
                        }}
                        className={styles.formNumberPicker}
                        type="inline"
                        name="unitPrice"
                        min={activityTypeGroup[consumptionActivityType].minValue}
                        precision={consumptionActivityType === 0 ? 2 : 0}
                        max={activityTypeGroup[consumptionActivityType].maxValue}
                        defaultValue={1}
                        key={formData.taskRequestList[index]}
                        value={formData.taskRequestList[index].amount}
                        disabled={activityEditDisabled()}
                      />
                      {activityTypeGroup[consumptionActivityType].activityUnit}
                    </div>
                  );
                }}
              />
              <Table.Column
                title="奖品名称"
                dataIndex="prizeName"
                cell={(_, index, row) => <div>{row.prizeList[0].prizeName}</div>}
              />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeList[0].prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeList[0].prizeType === 1) {
                    return <div>{row.prizeList[0].numPerSending ? `${row.prizeList[0].numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.prizeList[0].unitCount ? `${row.prizeList[0].unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeList[0].prizeType] && row.prizeList[0].unitPrice === 0
                      ? row.prizeList[0].unitPrice
                      : row.prizeList[0].unitPrice
                      ? Number(row.prizeList[0].unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => (
                  <div>{row.prizeList[0].sendTotalCount ? `${row.prizeList[0].sendTotalCount}份` : ''}</div>
                )}
              />
              {!activityEditDisabled() && (
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    <FormItem
                      style={{ marginBottom: '0' }}
                      disabled={isDisableSetPrize(formData.taskRequestList, index)}
                    >
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.taskRequestList[index];
                          if (!row.prizeList[0].prizeName) {
                            row = null;
                          }
                          setEditValue(row ? row.prizeList[0] : null);
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                      </Button>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          const newPlan = plan.filter(
                            (item) => item !== formData.taskRequestList[index].prizeList[0].prizeKey,
                          );
                          setPlan(newPlan);
                          formData.taskRequestList.splice(index, 1);
                          setData(formData);
                        }}
                      >
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                    </FormItem>
                  )}
                />
              )}
            </Table>
            <Button
              type={'primary'}
              disabled={activityEditDisabled() || formData.taskRequestList.length >= 4}
              onClick={addPrize}
              style={{ marginTop: 10 }}
            >
              添加奖项({formData.taskRequestList.length}/4)
            </Button>
            <LzDialog
              title={false}
              visible={visible}
              footer={false}
              onClose={() => setVisible(false)}
              style={{ width: '670px' }}
            >
              <ChoosePrize
                formData={formData}
                editValue={editValue}
                hasLimit={false}
                hasProbability={false}
                typeList={[1, 2, 3, 4, 6, 7, 8, 9, 10, 12]}
                onChange={onPrizeChange}
                onCancel={onCancel}
                planList={plan}
              />
            </LzDialog>
          </FormItem>
          <FormItem label="订单商品" required>
            <Radio.Group
              value={formData.assignGoodsFlag}
              onChange={(assignGoodsFlag: number) => {
                formData.skuList = [];
                formData.skuListPreview = [];
                setData({ assignGoodsFlag, skuList: formData.skuList });
              }}
              disabled={activityEditDisabled()}
            >
              <Radio id="0" value={0}>
                全部商品<span className={styles.tip}>(不含虚拟商品)</span>
              </Radio>
              <Radio id="1" value={1}>
                指定商品
              </Radio>
              {/* <Radio id="2" value={2}> */}
              {/*  排除商品 */}
              {/* </Radio> */}
            </Radio.Group>
            <Grid.Row>
              {formData.assignGoodsFlag === 1 && (
                <FormItem name="skuList" required requiredMessage={'请选择指定商品'} style={{ marginTop: '15px' }}>
                  <ChooseGoods value={formData.skuList} onChange={handleSkuChange} disabled={activityEditDisabled()} />
                  <SkuList skuList={formData.skuList} handlePreview={handlePreview} removeSku={(data:any) => {
                      const skuList1 = formData.skuList.filter((item:any) => item.skuId !== data);
                      formData.skuList = skuList1;
                      setData({ skuList: formData.skuList });
                    }} />
                  <Input className="validateInput" name="skuList" value={formData.skuList} />
                </FormItem>
              )}
              {formData.assignGoodsFlag === 2 && (
                <FormItem name="skuList" required requiredMessage={'请选择排除商品'} style={{ marginTop: '15px' }}>
                  <ChooseGoods value={formData.skuList} onChange={handleSkuChange} disabled={activityEditDisabled()} />
                  <SkuList skuList={formData.skuList} handlePreview={handlePreview} removeSku={(data:any) => {
                      const skuList1 = formData.skuList.filter((item:any) => item.skuId !== data);
                      formData.skuList = skuList1;
                      setData({ skuList: formData.skuList });
                    }} />
                  <Input className="validateInput" name="skuList" value={formData.skuList} />
                </FormItem>
              )}
            </Grid.Row>
            <div className={styles['tips-box']}>
              <div className={styles['gray-text']} style={{ marginRight: '5px' }}>
                备注
              </div>
              <div>
                <div className={styles['gray-text']}>
                  1，订单商品即正装商品，指用户为参与活动，其所购买的订单中所应包含的商品SKUID；
                </div>
                <div className={styles['gray-text']}>
                  2，如选择【指定商品】，C端活动页面将同步展示被添加的商品，上限展示500个；
                </div>
                <div className={styles['gray-text']}>
                  3，如选择【全部商品】，请注意C端活动页面将不展示推广商品，进入活动的用户需通过页面上的店铺入口，自行到店铺进行商品购买，请谨慎配置；
                </div>
              </div>
            </div>
          </FormItem>
          {consumptionActivityType === 0 && (
            <FormItem label="价格类型" required>
              <Radio.Group
                value={formData.moneyType}
                onChange={(moneyType: number) => {
                  setData({ moneyType });
                }}
                disabled={activityEditDisabled()}
              >
                <Radio id="0" value={0}>
                  京东价
                </Radio>
                <Radio id="1" value={1}>
                  实付价
                </Radio>
              </Radio.Group>
              {/* <div className={styles['price-tips-box']}>
                <div>注意：</div>
                <div className={styles['tips-line']}>
                  1.
                  背景：由于平台侧只提供POP店铺整笔订单的实付价金额，并不提供此订单中单个商品（例：整笔订单中包含多个商品）的实付价金额；
                </div>
                <div className={styles['tips-line']}>
                  2. 影响：因此【云鹿】通过整笔订单的折扣力度（订单实付价金额 /
                  订单京东价金额）来计算，该笔订单中单个商品的实付价金额；此方式计算的单个商品实付价金额将会与消费者实际的实付价金额有一定的出入，望知悉；
                </div>
                <div className={styles['tips-line']}>
                  3. 另：因为平台并不提供该笔订单的运费数据（有的店铺需要额外支付运费），因此有极小概率会出现：
                  【云鹿】计算的实付价金额 大于 京东价；
                </div>
                <div className={styles['tips-line']}>4. 请您谨慎选择价格类型。</div>
              </div> */}
            </FormItem>
          )}
          <FormItem label="领取方式" required>
            <Radio.Group
              value={formData.prizeMethod}
              onChange={(prizeMethod: number) => {
                if (formData.moneyType === 0) {
                  setData({ prizeMethod, delayPrizeFlag: false });
                } else {
                  setData({ prizeMethod });
                }
              }}
              disabled={activityEditDisabled()}
            >
              <Radio id="0" value={0}>
                单次领取
              </Radio>
              <Radio id="1" value={1}>
                全部领取
              </Radio>
            </Radio.Group>
            <div className={styles['price-tips-box']}>
              <div>注意：</div>
              <div className={styles['tips-line']}>
                1. 当只设置一个奖励条件时，单次领取和全部领取均为限制消费者仅能领取1次奖励；
              </div>
              <div className={styles['tips-line']}>
                2.
                当设置多个奖励条件时，单次领取为限制消费者仅能领取1次奖励（如果消费者满足多个领奖条件时，支持消费者自由选择领取哪一个奖励）；全部领取则为消费者可以获得全部对应的奖励。
              </div>
              <div className={styles['tips-line']}>
                3.若希望预售的消费者能够参加本次活动，请在设置本活动的下单时间时，将预售开始时间包含在内（PS：消费者交付定金的时间即为该消费者的下单时间）
              </div>
            </div>
          </FormItem>
          {consumptionActivityType === 0 && (
            <FormItem label="延迟发奖" required>
              <FormItem>
                <Radio.Group
                  value={formData.delayPrizeFlag ? 1 : 0}
                  onChange={(delayPrizeFlag: number) => {
                    if (delayPrizeFlag === 1) {
                      setData({ delayPrizeFlag: delayPrizeFlag === 1, endActivity: false });
                    } else {
                      setData({ delayPrizeFlag: delayPrizeFlag === 1 });
                    }
                  }}
                  disabled={activityEditDisabled() || formData.moneyType === 0}
                >
                  <Radio id="1" value={1}>
                    是
                  </Radio>
                  <Radio id="0" value={0}>
                    否
                  </Radio>
                </Radio.Group>
              </FormItem>
              {formData.delayPrizeFlag && (
                <FormItem required requiredMessage="请输入延迟发放天数">
                  延迟发放{' '}
                  <NumberPicker
                    name="delayPrizeDays"
                    min={1}
                    max={60}
                    precision={0}
                    type="inline"
                    value={formData.delayPrizeDays}
                    onChange={(delayPrizeDays: number) => setData({ delayPrizeDays })}
                    disabled={activityEditDisabled()}
                  />{' '}
                  天
                </FormItem>
              )}
            </FormItem>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
