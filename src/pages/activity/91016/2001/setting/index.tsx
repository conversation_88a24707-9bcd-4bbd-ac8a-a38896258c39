import React, { useImperativeHandle, useRef, useReducer } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 权益设置
import ParticipationRulesInfo from './components/ParticipationRules';
// 参与规则
import ActivityMonitorInfo from './components/ActivityMonitor';
// 活动监控
import ShareInfo from './components/Share';
// 活动规则
import RulesInfo from './components/Rules';
import { PageData } from '../util';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onSubmit: (resultListLength: number) => void;
  decoValue: any;
  consumptionActivityType: any;
}
interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}
export default ({
  onSettingChange,
  defaultValue,
  value,
  sRef,
  onSubmit,
  decoValue,
  consumptionActivityType,
}: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const onChange = (activityInfo, type?: string): void => {
    console.log('活动设置信息更新:', activityInfo);
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value: formData,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const equityRef = useRef<{ submit: () => void | null }>(null);
  const activityMonitorRef = useRef<{ submit: () => void | null }>(null);
  const shareRef = useRef<{ submit: () => void | null }>(null);
  const rulesRef = useRef<{ submit: () => void | null }>(null);

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current!,
        equityRef.current!,
        activityMonitorRef.current!,
        shareRef.current!,
        rulesRef.current!,
      ];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        const result = events[index].submit();
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  return (
    <div className={styles.setting}>
      <BaseInfo sRef={baseRef} {...settingProps} />
      <ParticipationRulesInfo sRef={equityRef} {...settingProps} consumptionActivityType={consumptionActivityType} />
      <ActivityMonitorInfo
        sRef={activityMonitorRef}
        {...settingProps}
        consumptionActivityType={consumptionActivityType}
      />
      <ShareInfo
        sRef={shareRef}
        {...settingProps}
        decoValue={decoValue}
        consumptionActivityType={consumptionActivityType}
      />
      <RulesInfo sRef={rulesRef} {...settingProps} />
    </div>
  );
};
