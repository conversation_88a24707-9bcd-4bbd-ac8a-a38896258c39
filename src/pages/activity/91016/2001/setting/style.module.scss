.setting {
  margin-top: 15px;
}

.panel {
  box-sizing: border-box;
  background: #f5f7f9;
  padding: 15px;
  border: 1px solid #d7dde4;
  min-width: 350px;
}
.number {
  margin: 0 10px;
}
.tip {
  font-size: 12px;
  color: gray;
  margin-top: 15px;
}
.tips {
  font-size: 12px;
}
.point-input {
  margin-right: 5px;
  width: 130px !important;
}
.exchange-input {
  margin-right: 5px;
  width: 150px !important;
}
.cycle-select {
  margin-right: 5px;
  width: 130px;
}
.select-time {
  width: 100px;
}
.select-time-line {
  margin: 0 5px;
}
.form-item-style {
  height: 30px;
  line-height: 30px;
}
.choose-promotion-tips {
  margin-top: 6px;
  color: #ff3333;
  .choose-promotion-tips-line2 {
    margin: 5px  0 0 36px;
  }
}
.help-icon-style {
  margin-left: 2px;
  line-height: 15px;
}
.input-width {
  width: 200px !important;
}
.input-width1 {
  width: 500px !important;
}
.point-unit {
  margin: 0 5px;
  height: 28px;
  line-height: 28px;
}
.unit-style {
  margin: 0 5px;
}
.ballon-img {
  width: 900px;
}
.choose-goods-box {
  display: flex;
}
.choose-goods-tips {
  color: #c7c7c7;
}
.goods-img {
  margin-right: 5px;
  width: 60px;
  height: 60px;
}
.flex-box {
  display: flex;
}
.line-height {
  line-height: 28px;
}
.tips-box {
  //margin-top: 7px;
  display: flex;
  line-height: 28px;
  .gray-text {
    color:rgb(187, 195, 204);
  }
  .red-text {
    color: red;
  }
}

.gray-text-box {
  margin-top: 10px;
  color:rgb(187, 195, 204);
}
.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  margin-top: 15px;

  .skuContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;
    position: relative;

    &:hover {
      .del {
        display: block;
      }
    }

    .skuImg {
      margin-right: 10px;
      width: 60px;
      height: 60px;
    }

    .skuName {
      max-width: 120px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}

.del {
  position: absolute;
  right: -5px;
  top: -5px;
  line-height: 16px;
  color: #9CA7B6;
  display: none;
  cursor: pointer;
}
.tips-bg {
  margin-left: 20px;
  font-size: 12px;
  background: #f0faff;
}
.formNumberPicker {
  margin: 0 2px;
}

.price-tips-box {
  margin-top: 10px;
  padding: 10px;
  background-color: #F0FAFF;
  border-radius: 5px;
  .tips-line {
    margin-left: 27px;
    line-height: 20px;
  }
}
