import React, { useEffect, useReducer, useState } from 'react';
import { Form, Input, Table } from '@alifd/next';
import { activityTypeGroup, formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import SkuList from '@/components/SkuList';
import { getShopId } from '@/utils/shopUtil';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动设置信息" labelAlign={'left'}>
          <FormItem label="活动标题：">
            <div className={styles['form-item-info']}>{formData.activityName}</div>
          </FormItem>
          <FormItem label="活动时间：">
            <div className={styles['form-item-info']}>{`${format.formatDateTimeDayjs(
              formData.rangeDate[0],
            )}至${format.formatDateTimeDayjs(formData.rangeDate[1])}`}</div>
          </FormItem>
          <FormItem label="活动门槛：">
            <div>{generateMembershipString(formData)}</div>
          </FormItem>
        </FormItem>
        <FormItem label="参与规则" labelAlign={'left'}>
          <FormItem label="下单时间：">
            <div className={styles['form-item-info']}>{`${format.formatDateTimeDayjs(
              formData.orderStartTime[0],
            )}至${format.formatDateTimeDayjs(formData.orderStartTime[1])}`}</div>
          </FormItem>
          <FormItem label="消费类型：">
            <div className={styles['form-item-info']}>
              {activityTypeGroup[formData.configurationType].consumptionActivityType}
            </div>
          </FormItem>
          {formData.configurationType === 2 && (
            <FormItem label="单笔订单金额：">
              <div className={styles['form-item-info']}>大于等于{formData.fullTimesMoney}元</div>
            </FormItem>
          )}
          <FormItem label="奖励设置：">
            <Table dataSource={formData.taskRequestList}>
              <Table.Column
                title={<div className={styles['table-td']}>领奖条件</div>}
                cell={(_, index, row) => {
                  return (
                    <div className={styles['table-td']}>
                      累计消费{row.amount}
                      {activityTypeGroup[formData.configurationType].activityUnit}
                    </div>
                  );
                }}
              />
              <Table.Column
                title={<div className={styles['table-td']}>奖品名称</div>}
                dataIndex="prizeName"
                cell={(_, index, row) => <div className={styles['table-td']}>{row.prizeList[0].prizeName}</div>}
              />
              <Table.Column
                title={<div className={styles['table-td']}>奖品类型</div>}
                cell={(_, index, row) => (
                  <div className={styles['table-td']}>{PRIZE_TYPE[row.prizeList[0].prizeType]}</div>
                )}
                dataIndex="prizeType"
              />
              <Table.Column
                title={<div className={styles['table-td']}>单位数量</div>}
                cell={(_, index, row) => {
                  if (row.prizeList[0].prizeType === 1) {
                    return (
                      <div className={styles['table-td']}>
                        {row.prizeList[0].numPerSending ? `${row.prizeList[0].numPerSending}张` : ''}
                      </div>
                    );
                  } else {
                    return (
                      <div className={styles['table-td']}>
                        {row.prizeList[0].unitCount ? `${row.prizeList[0].unitCount}份` : ''}
                      </div>
                    );
                  }
                }}
              />
              <Table.Column
                title={<div className={styles['table-td']}>单份价值(元)</div>}
                cell={(_, index, row) => (
                  <div className={styles['table-td']}>
                    {PRIZE_TYPE[row.prizeList[0].prizeType] && row.prizeList[0].unitPrice === 0
                      ? row.prizeList[0].unitPrice
                      : row.prizeList[0].unitPrice
                      ? Number(row.prizeList[0].unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title={<div className={styles['table-td']}>发放份数</div>}
                cell={(_, index, row) => (
                  <div className={styles['table-td']}>
                    {row.prizeList[0].sendTotalCount ? `${row.prizeList[0].sendTotalCount}份` : ''}
                  </div>
                )}
              />
            </Table>
          </FormItem>
          <FormItem label="订单商品：">
            <div className={styles['form-item-info']}>
              {formData.assignGoodsFlag === 0 && (
                <div>
                  全部商品<span className={styles.tip}>(不含虚拟商品)</span>
                </div>
              )}
              {formData.assignGoodsFlag === 1 && <div>指定商品</div>}
              {formData.assignGoodsFlag === 2 && <div>排除商品</div>}
            </div>

            {formData.assignGoodsFlag === 1 || formData.assignGoodsFlag === 2 ? (
              <div className={styles.container}>
                <SkuList skuList={formData.skuList} />
              </div>
            ) : (
              ''
            )}
          </FormItem>
          {/* <FormItem label="订单商品："> */}
          {/*  {formData.assignGoodsFlag === 0 && <div className={styles['form-item-info']}>全部商品</div>} */}
          {/*  {formData.assignGoodsFlag === 1 && ( */}
          {/*    <div className={styles['form-item-info']}> */}
          {/*      指定商品 */}
          {/*      <div className={styles.container}> */}
          {/*        <SkuList skuList={formData.skuList} /> */}
          {/*      </div> */}
          {/*    </div> */}
          {/*  )} */}
          {/*  {formData.assignGoodsFlag === 2 && ( */}
          {/*    <div className={styles['form-item-info']}> */}
          {/*      排除商品 */}
          {/*      <div className={styles.container}> */}
          {/*        <SkuList skuList={formData.skuList} /> */}
          {/*      </div> */}
          {/*    </div> */}
          {/*  )} */}
          {/* </FormItem> */}
          { formData.configurationType === 0 && (
            <FormItem label="价格类型：">
              <div className={styles['form-item-info']}>{formData.moneyType === 0 ? '京东价' : '实付价'}</div>
            </FormItem>
          )}
          <FormItem label="领取方式：">
            <div className={styles['form-item-info']}>{formData.prizeMethod === 0 ? '单次领取' : '全部领取'}</div>
          </FormItem>
          {formData.configurationType === 0 && (
            <>
              <FormItem label="延迟发奖：">
                <div className={styles['form-item-info']}>
                  {formData.delayPrizeFlag ? `延迟发放${formData.delayPrizeDays}天` : '否'}
                </div>
              </FormItem>
            </>
          )}
        </FormItem>
        <FormItem label="活动监控" labelAlign={'left'}>
          <FormItem label="活动强制结束：">
            <div className={styles['form-item-info']}>
              {formData.endActivity ? '全部奖品发完强制结束活动' : '全部奖品发完活动不结束'}
            </div>
          </FormItem>
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="图文分享标题" labelAlign={'left'}>
              <div className={styles['form-item-info']}>{formData.shareTitle}</div>
            </FormItem>
            <FormItem label="图文分享图片" labelAlign={'left'}>
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片" labelAlign={'left'}>
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片" labelAlign={'left'}>
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容" labelAlign={'left'}>
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
