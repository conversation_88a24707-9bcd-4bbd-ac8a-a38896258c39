import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import format from '@/utils/format';
import Preview from '@/components/LzCrowdBag/preview';
import React from 'react';

export interface PrizeListInfo {
  prizeImg: string;
  prizeName: string;
  prizeType: number;
  sendTotalCount: number;
  // 单位数量
  unitCount: number;
  // 单位价值
  unitPrice: number;
}
const PRIZE_INFO = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
};
export interface PrizeInfo {
  prizeList: PrizeListInfo[];
  amount: number;
  sort: number;
  id: any;
}
export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export interface PageData {
  activityName: string;
  assignGoodsFlag: number;
  cmdImg: string;
  configurationType: number;
  crowdBag: any;
  endActivity: number;
  endTime: string;
  h5Img: string;
  mpImg: string;
  orderStartTime: [Dayjs, Dayjs];
  rangeDate: [Dayjs, Dayjs];
  fullTimesMoney: number;
  rules: string;
  shareStatus: number;
  shareTitle: string;
  skuList: any;
  startTime: string;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  moneyType: any;
  prizeMethod: any;
  delayPrizeFlag: boolean;
  delayPrizeDays: number;
  taskRequestList: PrizeInfo[];
  templateCode: string;
  threshold: number;
  gradeLabel: string[];
  shopType: number;
}
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
export const activityTypeGroup = [
  {
    consumptionActivityType: '满额有礼',
    activityUnit: '元',
    minValue: 0,
    maxValue: 999999,
    actBg: '//img10.360buyimg.com/imgzone/jfs/t1/234208/36/15632/82842/66053855F3b688fa4/0f5eb03164d006d5.png',
    tipsText: '用户【累计消费的总金额】满足对应条件后，即有机会获得对应奖励;',
    h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
    cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/241153/12/6372/74617/6614de61Fe1d96b59/c4a7e883db680607.png',
    mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/179756/23/43990/48472/6614de61F27aa5d1a/7f5b4de6378d9ac4.jpg',
  },
  {
    consumptionActivityType: '满件有礼',
    activityUnit: '件',
    minValue: 1,
    maxValue: 999,
    actBg: '//img10.360buyimg.com/imgzone/jfs/t1/190882/9/37183/101747/660a230eF4146ba2e/dcf6a860c13cc4c4.png',
    tipsText: '用户【累计消费的商品件数】满足门槛条件后，即有机会获得设置的奖励;',
    h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/155496/28/35706/11073/6614de62Fad2c41d0/49cdc4df27b87852.png',
    cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/242585/20/6811/74274/6614de60F0848efb0/e14eba83862c1033.png',
    mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/237673/4/14636/47683/6614de61F34a1b83c/3ec2463b9146a9d2.jpg',
  },
  {
    consumptionActivityType: '满次有礼',
    activityUnit: '次',
    minValue: 1,
    maxValue: 999,
    actBg: '//img10.360buyimg.com/imgzone/jfs/t1/231015/23/15830/102392/660a236dF84cf17de/cf1792dd21f75529.png',
    tipsText: '用户【累计消费订单次数】满足门槛条件后，即有机会获得设置的奖励;',
    h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/199826/15/43748/11083/6614de62F03ec89b1/e2ee2386cf609174.png',
    cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/249243/11/7065/75493/6614de61F8ab5acc8/e1844e98fe6cd200.png',
    mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/190865/31/43669/47870/6614de61F85cbf275/ce9fbcbb1aabd0db.jpg',
  },
];
export interface CustomValue {
  actBg: string;
  actBgColor: string;
  shopNameColor: string;
  btnBg: string;
  btnTextColor: string;
  btnBorderColor: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  consumptionActivityType: any;
  disableShopName: number;
}
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE_DATA: CustomValue = {
  // 奖品背景图
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/168651/1/42285/110229/65e98012Fdb9af44b/f90a71317b8fa632.png',
  // 页面背景颜色
  actBgColor: '',
  // 店铺名称颜色
  shopNameColor: '',
  btnBg: '',
  btnTextColor: '',
  btnBorderColor: '',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/241153/12/6372/74617/6614de61Fe1d96b59/c4a7e883db680607.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/179756/23/43990/48472/6614de61F27aa5d1a/7f5b4de6378d9ac4.jpg',
  consumptionActivityType: 0,
  disableShopName: 0,
};
// 活动设置默认数据
export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `${
      activityTypeGroup[DEFAULT_CUSTOM_VALUE_DATA.consumptionActivityType].consumptionActivityType
    }-${dayjs().format('YYYY-MM-DD')}`,
    assignGoodsFlag: 0,
    configurationType: DEFAULT_CUSTOM_VALUE_DATA.consumptionActivityType,
    // 活动结束时间
    crowdBag: null,
    endActivity: 0, // 活动强制结束 0否 1是
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
    cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/241153/12/6372/74617/6614de61Fe1d96b59/c4a7e883db680607.png',
    mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/179756/23/43990/48472/6614de61F27aa5d1a/7f5b4de6378d9ac4.jpg',
    moneyType: 0,
    prizeMethod: 0,
    delayPrizeFlag: false,
    delayPrizeDays: 7,
    orderStartTime: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    fullTimesMoney: 0,
    rules: '',
    shareStatus: 1,
    shareTitle: `${activityTypeGroup[DEFAULT_CUSTOM_VALUE_DATA.consumptionActivityType].consumptionActivityType}`,
    skuList: [],
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    supportLevels: '1,2,3,4,5,-9',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    taskRequestList: [
      {
        prizeList: [{ ...PRIZE_INFO }],
        amount: 1,
        id: `id-${Math.random().toString(36).substr(2, 9)}`,
        sort: 1,
      },
    ],
    templateCode: '1006',
    threshold: 1, // 后端需要的一个参数，参数值与autoStopAct一致
    gradeLabel: [], // 根据实际情况，可能需要定义奖品的类型
    shopType: 0,
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

const isOrderFinishStartTimeValid = (
  activityStartTime: string,
  activityEndTime: string,
  orderFinishStartTime: any,
  orderFinishEndTime: any,
): boolean => {
  // const isGreaterThanStartBefore: boolean = dayjs(orderFinishStartTime).isAfter(
  //   dayjs(activityStartTime).subtract(1, 'month'),
  // );
  const isGreaterEnd: boolean = dayjs(activityEndTime).isAfter(orderFinishEndTime);
  // if (!isGreaterThanStartBefore) {
  //   Message.error('【订单完成时间】开始时间，最多只能早于活动开始时间1个月');
  //   return false;
  // }
  if (dayjs(orderFinishEndTime).format('YYYY-MM-DD') !== dayjs(activityEndTime).format('YYYY-MM-DD') && !isGreaterEnd) {
    Message.error('【订单完成时间】结束时间,不能晚于活动结束时间');
    return false;
  }
  return true;
};
const hasTime = (formData): boolean => {
  if (!formData.startTime || !formData.endTime) {
    Message.error('请选择活动时间');
    return false;
  }
  return true;
};
export const PRIZE_TYPE: any = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  5: '折扣商品',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '令牌',
};
const checkPrizeTime = (formData: PageData, prizeInfo): any => {
  // 开始时间
  const start = prizeInfo.startTime || prizeInfo.startDate || prizeInfo.beginTime;
  // 结束时间
  const end = prizeInfo.endTime || prizeInfo.endDate;
  if (!prizeInfo.prizeType) {
    Message.error('请选择奖品');
    return false;
  }
  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).startOf('second').isBefore(dayjs(start));
    if (isStart) {
      Message.error(`${PRIZE_TYPE[prizeInfo.prizeType]}开始时间应小于活动开始时间`);
      return false;
    }
    if (formData.delayPrizeFlag) {
      // 奖品的结束时间间是否大于活动的结束时间
      const isEnd: boolean = dayjs(formData.endTime)
        .startOf('second')
        .add(formData.delayPrizeDays, 'day')
        .isAfter(dayjs(end));
      if (isEnd) {
        Message.error(`${PRIZE_TYPE[prizeInfo.prizeType]}结束时间需要晚于或等于活动结束时间后延迟发奖天数`);
        return false;
      }
    } else {
      // 奖品的结束时间间是否大于活动的结束时间
      const isEnd: boolean = dayjs(formData.endTime).startOf('second').isAfter(dayjs(end));
      if (isEnd) {
        Message.error(`${PRIZE_TYPE[prizeInfo.prizeType]}结束时间需要晚于或等于活动结束时间`);
        return false;
      }
    }
  }
  return true;
};
const isPrizeValid = (formData: PageData): boolean => {
  const awardConditionsArr: any = [];
  formData.taskRequestList.forEach((item) => {
    awardConditionsArr.push(item.amount);
  });
  if (formData.taskRequestList.findIndex((item) => !checkPrizeTime(formData, item.prizeList[0])) > -1) {
    return false;
  }
  for (let i = 0; i < awardConditionsArr.length; i++) {
    if (awardConditionsArr[i] >= awardConditionsArr[i + 1]) {
      Message.error('领奖条件必须递增');
      return false;
    }
  }
  if (awardConditionsArr.indexOf(0) > -1) {
    Message.error('请填写领奖条件');
    return false;
  }
  return true;
};
// eslint-disable-next-line complexity
export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  if (!hasTime(formData)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (
    !isOrderFinishStartTimeValid(
      formData.startTime,
      formData.endTime,
      formData.orderStartTime[0],
      formData.orderStartTime[1],
    )
  ) {
    return false;
  }
  if ((formData.threshold !== 1 || !/[1-5]/.test(formData.supportLevels)) && formData.moneyType === 1) {
    Message.error('实付价仅支持店铺会员参与，请设置会员门槛');
    return false;
  }
  if (formData.taskRequestList.length === 0) {
    Message.error('请选择奖品');
    return false;
  }
  if (!isPrizeValid(formData)) {
    return false;
  }
  if (formData.assignGoodsFlag === 1 && formData.skuList.length === 0) {
    Message.error('请选择订单商品');
    return false;
  }
  return true;
};
