import React from 'react';
import { Balloon, Icon, Radio } from '@alifd/next';

export default (props: any) => {
  const { exportTypeTitle, exportType, setExportType } = props;
  return (
    <div>
      <Radio.Group
        value={exportType}
        onChange={(v: number) => {
          setExportType(v);
        }}
      >
        <Radio id="0" value={1}>
          仅导出【{exportTypeTitle}】
        </Radio>
        <Radio id="1" value={2}>
          导出【参与记录】和【中奖记录】
          <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
            选择此项后，系统会将【参与记录】与【中奖记录】进行归类合并至一个sheet页中，并将合并后的数据进行导出
          </Balloon.Tooltip>
        </Radio>
      </Radio.Group>
    </div>
  );
};
