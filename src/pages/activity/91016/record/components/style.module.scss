.copy {
  margin-left: 8px;
  font-size: 10px;
  cursor: pointer;

  &:before {
    vertical-align: sub;
  }
}
.tooltip-title {
  display: flex;
  align-items: center;
}
.sku-not-click {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp:2;
  -webkit-box-orient: vertical;
}
.sku-click {
  color: #1677ff;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp:2;
  -webkit-box-orient: vertical;
}
.goods-info-box {
  display: flex;
  cursor: pointer;
  width: 284px;
  height: 52px;
  .goods-img {
    margin-right: 20px;
    width: 52px;
    height: 52px;
  }
  .goods-info {
    height: 35px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp:2;
    -webkit-box-orient: vertical;
  }
  &:hover {
    background-color: #F4F6F9;
    color: #1677ff;
  }
}
.order-detail {
  display: flex;
  margin-bottom: 20px;
  .order-detail-box {
    display: flex;
    align-items: center;
    margin-right: 10px;
    padding: 20px 40px;
    height: 50px;
    border: 1px solid gray;
    border-radius: 3px
  }
}
.send-status-box {
  display: flex;
  align-items: center;
}
.export-btn-box {
  .export-cancel-btn {
    margin-right: 10px;
    width: 88px;
  }
  .export-confirm-btn {
    width: 88px;
  }
}
.order-detail-table :global {
  .next-table-row.next-table-row-level-1 {
    background: #f0f2f5 !important;
  }
}
