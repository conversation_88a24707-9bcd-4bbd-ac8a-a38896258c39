import React, { useEffect, useState } from 'react';
import { Table, Balloon } from '@alifd/next';
import { dataSkuDetail } from '@/api/v91016';
import { getParams } from '@/utils';
import styles from './style.module.scss';

export default (props) => {
  const { currentData, currentSkuId, currentRelatedOrders } = props;
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const loadData = (): void => {
    setLoading(true);
    dataSkuDetail({
      activityId: getParams('id'),
      encryptPin: currentData.encryptPin,
      skuId: currentSkuId,
      orderIds: currentRelatedOrders,
    })
      .then((res: any): void => {
        setTableData(res as any[]);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  useEffect(() => {
    loadData();
  }, []);
  return (
    <div>
      <Table className={styles['order-detail-table']} isTree dataSource={tableData} loading={loading}>
        <Table.Column title="序号" cell={(value, index, data) => <span>{index + 1}</span>} />
        <Table.Column align="center" title="SKUID" dataIndex="skuId" />
        <Table.Column
          align="center"
          title="商品信息"
          cell={(value, index, data) => (
            <div
              onClick={() => {
                window.open(data.url, '_blank');
              }}
              className={styles['goods-info-box']}
            >
              <img className={styles['goods-img']} alt="暂无图片" src={data.img} />
              <div className={styles['goods-info']}>
                <Balloon.Tooltip
                  v2
                  trigger={
                    <div>
                      <div className={styles['goods-info']}>{data.skuName}</div>
                    </div>
                  }
                  align="t"
                >
                  {data.skuName}
                </Balloon.Tooltip>
              </div>
            </div>
          )}
        />
        <Table.Column align="center" title="下单件数/件" dataIndex="skuNum" />
        <Table.Column title="下单金额/元" dataIndex="skuPrice" />
      </Table>
    </div>
  );
};
