import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button, Select, Balloon, Icon, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataAllRecordExport, dataJoinRecord, dataJoinRecordExport, dataJoinRecordUploadPin } from '@/api/v91016';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import { appHistory } from '@ice/stark-app';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';
import styles from './style.module.scss';
import { getVenderLevelRule } from '@/api/common';
import OrderDetail from '../components/OrderDetail';
import ExportDialog from '../components/exportDialog';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const MEMBER_USER = [
  { label: '全部', value: '-1' },
  { label: '是', value: 1 },
  { label: '否', value: 0 },
];
const toYlList = () => {
  appHistory.push('/crowd/own/list/manage');
};

const toJDList = () => {
  appHistory.push('/crowd/list/manage');
};
export default () => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [currentData, setCurrentData] = useState({});
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [memberLevels, setMemberLevels] = useState([]);
  const [packVisible, setPackVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [exportVisible, setExportVisible] = useState(false);
  const [exportType, setExportType] = useState(1);
  const [currentParams, setCurrentParams] = useState({});
  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const getData = () => {
    setLoading(true);
    getVenderLevelRule()
      .then((data) => {
        // setMemberLevels(data as any);
        const newMemberLevel: any = [{ label: '全部', value: '-1' }];
        data.forEach((item) => {
          newMemberLevel.push({ label: item.customerLevelName, value: item.customerLevel });
        });
        setMemberLevels(newMemberLevel);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const loadData = (query: any): void => {
    if (query.level.length === 0) {
      Message.error('请选择会员等级');
      return;
    }
    setLoading(true);
    setCurrentParams({
      activityId: getParams('id'),
      encryptPin: query.encryptPin,
      startTime: query.dateRange[0],
      endTime: query.dateRange[1],
      isMember: query.isMember,
      level: query.level,
      nickName: query.nickName,
      orderId: query.orderId,
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    });
    dataJoinRecord({
      activityId: getParams('id'),
      encryptPin: query.encryptPin,
      startTime: query.dateRange[0],
      endTime: query.dateRange[1],
      isMember: query.isMember,
      level: query.level,
      nickName: query.nickName,
      orderId: query.orderId,
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    })
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(deepCopy(pageInfo));
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    const params = {
      activityId: getParams('id'),
      encryptPin: formValue.encryptPin,
      startTime: formValue.dateRange[0],
      endTime: formValue.dateRange[1],
      isMember: formValue.isMember,
      level: formValue.level,
      nickName: formValue.nickName,
      orderId: formValue.orderId,
      pageNum: formValue.pageNum,
      pageSize: formValue.pageSize,
    };
    if (exportType === 1) {
      dataJoinRecordExport(params).then((data: any) => downloadExcel(data, '满额有礼参与记录'));
    } else {
      dataAllRecordExport(params).then((data: any) => downloadExcel(data, '满额有礼参与记录和中奖记录'));
    }
    setExportVisible(false);
  };
  const encryptStr = (pin: string): string => {
    const first = pin.slice(0, 1);
    const last = pin.slice(-1);
    return `${first}****${last}`;
  };
  const copyText = async (text: string) => {
    await Utils.copyText(text);
    Message.success('已成功复制至剪切板');
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
    getData();
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input defaultValue={''} maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="encryptPin" label="用户pin">
          <Input defaultValue={''} placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="dateRange" label="参与时间">
          <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={constant.DATE_FORMAT_TEMPLATE} />
        </FormItem>
        <FormItem name="orderId" label="订单号">
          <Input defaultValue={''} placeholder="请输入关联订单号" />
        </FormItem>
        <FormItem name="isMember" label="会员用户">
          <Select dataSource={MEMBER_USER} followTrigger defaultValue={'-1'} />
        </FormItem>
        <FormItem name="level" label="会员等级">
          <Select dataSource={memberLevels} followTrigger defaultValue={['-1']} mode="multiple" />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
          <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
            生成云鹿人群
            <Balloon v2 trigger={<Icon type="help" size="small" />} triggerType="hover" align="bl" closable={false}>
              <div>
                <div style={{ marginBottom: '5px' }}>1.云鹿人群均以当前的查询条件进行生成;</div>
                <div style={{ marginBottom: '5px', lineHeight: '20px' }}>
                  2.生成云鹿人群后，如需进行短信营销，请在
                  <span onClick={toYlList} style={{ cursor: 'pointer', color: '#1677ff' }}>
                    云鹿人群包列表
                  </span>
                  中将云鹿人群转换为京东人群再发送短信;转换后的人群可在
                  <span onClick={toJDList} style={{ cursor: 'pointer', color: '#1677ff' }}>
                    京东人群包列表
                  </span>
                  中查看并管理。
                </div>
              </div>
            </Balloon>
          </Button>
          <Button
            disabled={!tableData.length}
            onClick={() => {
              // setExportVisible(true);
              setExportType(1);
              exportData();
            }}
          >
            导出
          </Button>
        </FormItem>
      </Form>
      <Table dataSource={tableData} loading={loading} style={{ marginTop: '10px' }}>
        <Table.Column title="序号" cell={(value, index, data) => <div>{index + 1}</div>} />
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value, index, data) => (
            <div>
              {data.nickName ? data.nickName : '-'}
              {data.nickName && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    copyText(data.nickName);
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value, index, data) => (
            <div>
              {data.encryptPin ? encryptStr(data.encryptPin) : '-'}
              {data.encryptPin && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    copyText(data.encryptPin);
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column title="会员等级" dataIndex="level" />
        <Table.Column
          title="参与时间"
          width="200px"
          dataIndex="createTime"
          cell={(value, index, data) => (
            <div>{data.createTime ? format.formatDateTimeDayjs(data.createTime) : '-'}</div>
          )}
        />
        <Table.Column
          title="参与订单"
          cell={(value, index, data) => (
            <Button
              text
              type="primary"
              onClick={() => {
                setCurrentData(data);
                setDetailVisible(true);
              }}
            >
              订单详情
            </Button>
          )}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      {packVisible && (
        <LzDialog
          title="生成云鹿人群"
          className="lz-dialog-mini"
          visible={packVisible}
          footer={false}
          onCancel={() => setPackVisible(false)}
          onClose={() => setPackVisible(false)}
        >
          <LzGenerateCrowdBag
            dataUploadPin={dataJoinRecordUploadPin}
            formValue={currentParams}
            cancel={() => setPackVisible(false)}
          />
        </LzDialog>
      )}
      {detailVisible && (
        <LzDialog
          title="订单详情"
          visible={detailVisible}
          footer={false}
          width={1185}
          // height={500}
          onCancel={() => setDetailVisible(false)}
          onClose={() => setDetailVisible(false)}
        >
          <OrderDetail currentData={currentData} />
        </LzDialog>
      )}
      {exportVisible && (
        <LzDialog
          title="导出"
          visible={exportVisible}
          footer={
            <div className={styles['export-btn-box']}>
              <Button onClick={() => setExportVisible(false)} className={styles['export-cancel-btn']}>
                取消
              </Button>
              <Button type="primary" onClick={exportData} className={styles['export-confirm-btn']}>
                确认导出
              </Button>
            </div>
          }
          width={600}
          onCancel={() => setExportVisible(false)}
          onClose={() => setExportVisible(false)}
        >
          <ExportDialog
            exportTypeTitle={'参与记录'}
            exportType={exportType}
            setExportType={(v) => {
              setExportType(v);
            }}
          />
        </LzDialog>
      )}
    </div>
  );
};
