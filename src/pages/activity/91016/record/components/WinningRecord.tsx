import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button, Select, Balloon, Icon, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import {
  dataAllRecordExport,
  dataPrizeUserRecord,
  dataPrizeUserRecordExport,
  dataPrizeUserRecordUploadPin,
} from '@/api/v91016';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import { appHistory } from '@ice/stark-app';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';
import styles from './style.module.scss';
import ExportDialog from '../components/exportDialog';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const PRIZE_TYPE = [
  { label: '全部', value: '' },
  { label: '优惠券', value: 1 },
  { label: '京豆', value: 2 },
  { label: '实物', value: 3 },
  { label: '积分', value: 4 },
  { label: '红包', value: 6 },
  { label: '京东E卡', value: 8 },
  { label: 'PLUS会员卡', value: 9 },
  { label: '爱奇艺会员卡', value: 10 },
  { label: '折扣商品', value: 5 },
  { label: '令牌', value: 11 },
  { label: '礼品卡', value: 7 },
  { label: '京元宝', value: 12 },
];
const RECEIVE_STATE = [
  { label: '全部', value: '' },
  { label: '发放成功', value: 1 },
  { label: '发放失败', value: 2 },
];
const toYlList = () => {
  appHistory.push('/crowd/own/list/manage');
};

const toJDList = () => {
  appHistory.push('/crowd/list/manage');
};
export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [exportVisible, setExportVisible] = useState(false);
  const [exportType, setExportType] = useState(1);
  const [currentParams, setCurrentParams] = useState({});
  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs().format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const loadData = (query: any): void => {
    setLoading(true);
    setCurrentParams({
      activityId: getParams('id'),
      encryptPin: query.encryptPin,
      startTime: query.dateRange[0],
      endTime: query.dateRange[1],
      prizeName: query.prizeName,
      prizeType: query.prizeType,
      nickName: query.nickName,
      sendStatus: query.sendStatus,
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    });
    dataPrizeUserRecord({
      activityId: getParams('id'),
      encryptPin: query.encryptPin,
      startTime: query.dateRange[0],
      endTime: query.dateRange[1],
      prizeName: query.prizeName,
      prizeType: query.prizeType,
      nickName: query.nickName,
      sendStatus: query.sendStatus,
      pageNum: query.pageNum,
      pageSize: query.pageSize,
    })
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    const params = {
      activityId: getParams('id'),
      encryptPin: formValue.encryptPin,
      startTime: formValue.dateRange[0],
      endTime: formValue.dateRange[1],
      prizeName: formValue.prizeName,
      prizeType: formValue.prizeType,
      nickName: formValue.nickName,
      sendStatus: formValue.sendStatus,
      pageNum: formValue.pageNum,
      pageSize: formValue.pageSize,
    };
    if (exportType === 1) {
      dataPrizeUserRecordExport(params).then((data: any) => downloadExcel(data, '满额有礼中奖记录'));
    } else {
      dataAllRecordExport(params).then((data: any) => downloadExcel(data, '满额有礼参与记录和中奖记录'));
    }
    setExportVisible(false);
  };
  const encryptStr = (pin: string): string => {
    const first = pin.slice(0, 1);
    const last = pin.slice(-1);
    return `${first}****${last}`;
  };
  const copyText = async (text: string) => {
    await Utils.copyText(text);
    Message.success('已成功复制至剪切板');
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="encryptPin" label="用户pin" defaultValue={''}>
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="dateRange" label="中奖时间">
          <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={constant.DATE_FORMAT_TEMPLATE} />
        </FormItem>
        <FormItem name="prizeName" label="奖品名称" defaultValue={''}>
          <Input placeholder="请输入奖品名称" />
        </FormItem>
        <FormItem name="prizeType" label="奖品类型">
          <Select dataSource={PRIZE_TYPE} followTrigger defaultValue={''} />
        </FormItem>
        <FormItem name="sendStatus" label="发放状态">
          <Select dataSource={RECEIVE_STATE} followTrigger defaultValue={''} />
        </FormItem>
        <FormItem name="sendDate" label="发放时间">
          <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={constant.DATE_FORMAT_TEMPLATE} />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
          <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
            生成云鹿人群
            <Balloon v2 trigger={<Icon type="help" size="small" />} triggerType="hover" align="bl" closable={false}>
              <div>
                <div style={{ marginBottom: '5px' }}>1.云鹿人群均以当前的查询条件进行生成;</div>
                <div style={{ marginBottom: '5px', lineHeight: '20px' }}>
                  2.生成云鹿人群后，如需进行短信营销，请在
                  <span onClick={toYlList} style={{ cursor: 'pointer', color: '#1677ff' }}>
                    云鹿人群包列表
                  </span>
                  中将云鹿人群转换为京东人群再发送短信;转换后的人群可在
                  <span onClick={toJDList} style={{ cursor: 'pointer', color: '#1677ff' }}>
                    京东人群包列表
                  </span>
                  中查看并管理。
                </div>
              </div>
            </Balloon>
          </Button>
          <Button
            disabled={!tableData.length}
            onClick={() => {
              // setExportVisible(true);
              setExportType(1);
              exportData();
            }}
          >
            导出
          </Button>
        </FormItem>
      </Form>
      <Table dataSource={tableData} loading={loading} style={{ marginTop: '10px' }}>
        <Table.Column title="序号" cell={(value, index, data) => <div>{index + 1}</div>} />
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value, index, data) => (
            <div>
              {data.nickName ? data.nickName : '-'}
              {data.nickName && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    copyText(data.nickName);
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value, index, data) => (
            <div>
              {data.encryptPin ? encryptStr(data.encryptPin) : '-'}
              {data.encryptPin && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    copyText(data.encryptPin);
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column title="会员等级" dataIndex="memberLevel" />
        <Table.Column title="奖品类型" dataIndex="prizeType" />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column
          title="发放状态"
          cell={(value, index, data) => (
            <div className={styles['send-status-box']}>
              <div style={{ marginRight: '2px' }}>{data.status}</div>
              {data.status === '发放失败' && (
                <Balloon.Tooltip
                  v2
                  trigger={
                    <div>
                      <Icon type="help" size="small" />
                    </div>
                  }
                  align="t"
                >
                  {data.cause}
                </Balloon.Tooltip>
              )}
            </div>
          )}
        />
        <Table.Column
          title="发放时间"
          width="200px"
          cell={(value, index, data) => <div>{data.sendTime ? format.formatDateTimeDayjs(data.sendTime) : '-'}</div>}
        />
        <Table.Column title="订单号" dataIndex="orderId" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      {packVisible && (
        <LzDialog
          title="生成云鹿人群"
          className="lz-dialog-mini"
          visible={packVisible}
          footer={false}
          onCancel={() => setPackVisible(false)}
          onClose={() => setPackVisible(false)}
        >
          <LzGenerateCrowdBag
            dataUploadPin={dataPrizeUserRecordUploadPin}
            formValue={currentParams}
            cancel={() => setPackVisible(false)}
          />
        </LzDialog>
      )}
      {exportVisible && (
        <LzDialog
          title="导出"
          visible={exportVisible}
          footer={
            <div className={styles['export-btn-box']}>
              <Button onClick={() => setExportVisible(false)} className={styles['export-cancel-btn']}>
                取消
              </Button>
              <Button type="primary" onClick={exportData} className={styles['export-confirm-btn']}>
                确认导出
              </Button>
            </div>
          }
          width={600}
          onCancel={() => setExportVisible(false)}
          onClose={() => setExportVisible(false)}
        >
          <ExportDialog
            exportTypeTitle={'中奖记录'}
            exportType={exportType}
            setExportType={(v) => {
              setExportType(v);
            }}
          />
        </LzDialog>
      )}
    </div>
  );
};
