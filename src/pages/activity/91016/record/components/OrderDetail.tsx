import React, { useState, useEffect } from 'react';
import { Table, Balloon, Icon } from '@alifd/next';
import { getParams } from '@/utils';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';
import styles from './style.module.scss';
import SkuDetail from '../components/SkuDetail';
import { dataOrderDetail } from '@/api/v91016';

export default (props) => {
  const { currentData } = props;
  const [orderMoney, setOrderMoney] = useState('0');
  const [orderNum, setOrderNum] = useState('');
  const [currentSkuId, setCurrentSkuId] = useState('');
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [goodsInfoVisible, setGoodsInfoVisible] = useState(false);
  const [currentRelatedOrders, setCurrentRelatedOrders] = useState(false);

  const getOrder = (value, index, data) => {
    if (data.children.length === 0 && tableData.findIndex((v) => v.relatedOrders === data.relatedOrders) === -1) {
      // 如果没有子订单，且不是最外层数据
      return <span>子订单</span>;
    } else {
      return <span>{tableData.findIndex((v) => v.relatedOrders === data.relatedOrders) + 1}</span>;
    }
  };
  const loadData = (): void => {
    setLoading(true);
    dataOrderDetail({
      activityId: getParams('id'),
      encryptPin: currentData.encryptPin,
      orderId: currentData.orderId,
    })
      .then((res: any): void => {
        setTableData(res.list as any[]);
        setOrderMoney(res.orderMoney);
        setOrderNum(res.orderNum);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <div>
      <div className={styles['order-detail']}>
        {orderNum && (
          <div className={styles['order-detail-box']}>
            <div>
              订单总笔数: <span>{orderNum}</span>笔
            </div>
          </div>
        )}

        {+orderMoney !== 0 && (
          <div className={styles['order-detail-box']}>
            <div>
              订单总金额: <span>{orderMoney}</span>元
            </div>
          </div>
        )}
      </div>
      <Table
        className={styles['order-detail-table']}
        primaryKey="relatedOrders"
        isTree
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column align="left" title="序号" cell={getOrder} />
        <Table.Column align="center" title="关联订单" dataIndex="relatedOrders" />
        <Table.Column
          align="center"
          title="拆单"
          cell={(value, index, data) => <span>{data.children.length === 0 ? '否' : '是'}</span>}
        />
        <Table.Column
          title="下单时间"
          align="center"
          cell={(value, index, data) => (
            <div>{data.orderStartTime ? format.formatDateTimeDayjs(data.orderStartTime) : '-'}</div>
          )}
        />
        <Table.Column
          title={
            <div className={styles['tooltip-title']}>
              下单金额/元
              <Balloon.Tooltip
                v2
                trigger={
                  <div>
                    <Icon type="help" size="small" />
                  </div>
                }
                align="t"
              >
                京东价
              </Balloon.Tooltip>
            </div>
          }
          dataIndex="orderPrice"
        />
        <Table.Column
          align="center"
          title="SKU ID"
          cell={(value, index, data) => (
            <div
              className={`${
                data.children.length === 0
                  ? data.skuId.split(',').length > 1 && +data.skuNum > 1
                    ? styles['sku-click']
                    : styles['sku-not-click']
                  : styles['sku-not-click']
              }`}
              onClick={(v) => {
                setCurrentSkuId(data.skuId);
                setCurrentRelatedOrders(data.relatedOrders);
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                data.children.length === 0
                  ? data.skuId.split(',').length > 1 && +data.skuNum > 1
                    ? setGoodsInfoVisible(true)
                    : ''
                  : '';
              }}
            >
              {data.skuId}
            </div>
          )}
        />
        <Table.Column
          align="center"
          title={
            <div className={styles['tooltip-title']}>
              下单件数/件
              <Balloon.Tooltip
                v2
                trigger={
                  <div>
                    <Icon type="help" size="small" />
                  </div>
                }
                align="t"
              >
                该订单号下的所有sku累计件数
              </Balloon.Tooltip>
            </div>
          }
          dataIndex="skuNum"
        />
        <Table.Column align="center" title="订单状态" dataIndex="receiveStatus" width="140px" />
        <Table.Column
          align="center"
          title="订单完成时间"
          cell={(value, index, data) => (
            <div>{data.orderFinishTime ? format.formatDateTimeDayjs(data.orderFinishTime) : '-'}</div>
          )}
        />
      </Table>
      <LzDialog
        title="商品详情"
        visible={goodsInfoVisible}
        footer={false}
        onCancel={() => setGoodsInfoVisible(false)}
        onClose={() => setGoodsInfoVisible(false)}
      >
        <SkuDetail currentData={currentData} currentSkuId={currentSkuId} currentRelatedOrders={currentRelatedOrders} />
      </LzDialog>
    </div>
  );
};
