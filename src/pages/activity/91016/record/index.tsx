/**
 * 消费有礼数据报表
 */
import React, { useState } from 'react';
import LzPanel from '@/components/LzPanel';
import JoinRecord from './components/JoinRecord';
import { Tab } from '@alifd/next';
import WinningRecord from './components/WinningRecord';
import LzDocGuide from "@/components/LzDocGuide";

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="消费有礼数据报表" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="参与记录" key="1">
            <JoinRecord />
          </Tab.Item>
          <Tab.Item title="中奖记录" key="2">
            <WinningRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
