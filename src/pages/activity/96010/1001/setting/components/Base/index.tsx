import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Input, DatePicker2, Field, Radio, TimePicker2, Checkbox } from '@alifd/next';
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import LzThreshold from '@/components/LzThreshold';

const FormItem = Form.Item;

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const onTimeRangeChange = (partakeRangeData): void => {
    setData({
      partakeRangeData: [
        format.formatDateTimeDayjs(partakeRangeData[0], 'HH:mm:ss'),
        format.formatDateTimeDayjs(partakeRangeData[1], 'HH:mm:ss'),
      ],
      partakeStartTime: format.formatDateTimeDayjs(partakeRangeData[0], 'HH:mm:ss'),
      partakeEndTime: format.formatDateTimeDayjs(partakeRangeData[1], 'HH:mm:ss'),
    });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setData({
      rangeDate,
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };
  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };
  // 修改参与限制时间段
  const changePartake = () => (partake) => {
    if (partake === 0) {
      formData.partakeRangeData = [];
      formData.partakeStartTime = '';
      formData.partakeEndTime = '';
      setData({ formData });
    }
    setData({ partake });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="活动基本信息">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="活动名称" required requiredMessage="请输入活动名称" disabled={false}>
            <Input
              value={formData.activityName}
              placeholder="请输入活动名称"
              name="activityName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(activityName) => setData({ activityName })}
            />
          </FormItem>
          <FormItem
            label="活动时间"
            required
            requiredMessage="请选择活动时间"
            extra={<div className="next-form-item-help">注：活动时间需设置在软件的订购有效期内</div>}
          >
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <DateRangeDuration
              rangeDate={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
            />
          </FormItem>
          <FormItem label="参与时段限制">
            <FormItem>
              <Radio.Group value={formData.partake} onChange={changePartake()}>
                <Radio value={0}>关闭</Radio>
                <Radio value={1}>开启</Radio>
              </Radio.Group>
            </FormItem>
            {formData.partake === 1 && (
              <FormItem required requiredMessage="请选择参与时段">
                <TimePicker2.RangePicker
                  className="w-300"
                  name="partakeRangeData"
                  format="HH:mm:ss"
                  hasClear={false}
                  value={formData.partakeRangeData}
                  onChange={onTimeRangeChange}
                />
              </FormItem>
            )}
          </FormItem>
          <FormItem label="活动门槛" required>
            <Checkbox disabled value={1} checked>
              店铺会员
            </Checkbox>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
