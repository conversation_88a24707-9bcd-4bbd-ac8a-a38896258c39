import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '../ChoosePrizeForDZ';
import { getParams, deepCopy } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  // const operationType: string = getParams('type');
  // const isEdit: boolean = operationType === 'edit';
  const isStart = getParams('status') === '2';
  // const isDraft: boolean = operationType === 'draft';
  // 创建安全的初始数据
  const getSafeInitialData = () => {
    if (value) return deepCopy(value);
    if (defaultValue) return deepCopy(defaultValue);
    // 如果都没有，返回基本结构
    return {
      mainPrizeList: [],
      bottomPrizeList: [],
    };
  };

  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, getSafeInitialData());
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  const [bottomVisible, setBottomVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  const [bottomEditValue, setBottomEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [bottomTarget, setBottomTarget] = useState(0);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange(data);
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    const newFormData = deepCopy(formData);
    // 确保 mainPrizeList 存在
    if (!newFormData.mainPrizeList) {
      newFormData.mainPrizeList = [];
    }
    newFormData.mainPrizeList[target] = data;
    newFormData.mainPrizeList[target].status = 1;
    newFormData.mainPrizeList[target].sortId = target;
    setData(newFormData);
    setVisible(false);
  };

  /**
   * 新建/编辑兜底奖品回调
   * @param data
   */
  const onBottomPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    const newFormData = deepCopy(formData);
    // 确保 bottomPrizeList 存在
    if (!newFormData.bottomPrizeList) {
      newFormData.bottomPrizeList = [];
    }
    newFormData.bottomPrizeList[bottomTarget] = data;
    setData(newFormData);
    setBottomVisible(false);
  };

  useEffect((): void => {
    setFormData(getSafeInitialData());
  }, [value]);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  const onBottomCancel = (): void => {
    setBottomVisible(false);
  };

  return (
    <div>
      <LzPanel title="奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="奖品列表" required>
            <Table dataSource={formData.mainPrizeList || []} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.prizeName ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.prizeName ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="中奖弹窗内奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="礼品池内奖品图"
                cell={(_, index, row) => (
                  <LzImageSelector
                    width={200}
                    height={200}
                    value={row.topShowImg}
                    onChange={(topShowImg) => {
                      const newFormData = deepCopy(formData);
                      // 确保 mainPrizeList 存在
                      if (!newFormData.mainPrizeList) {
                        newFormData.mainPrizeList = [];
                      }
                      if (newFormData.mainPrizeList[index]) {
                        newFormData.mainPrizeList[index].topShowImg = topShowImg;
                        setData(newFormData);
                      }
                    }}
                  />
                )}
              />
              <Table.Column
                title="奖品状态"
                cell={(_, index, row) => (
                  <div style={{ color: row.status === 1 ? 'green' : 'red' }}>
                    {row && row.prizeName === '' ? '-' : row && row.status === 1 ? '已启用' : '已下架'}
                  </div>
                )}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, row) => {
                  return (
                    <FormItem style={{ paddingTop: '18px' }}>
                      <Button
                        text
                        type="primary"
                        disabled={false}
                        onClick={() => {
                          const rowData = formData.mainPrizeList?.[index];
                          if (rowData?.prizeName === '') {
                            setEditValue(null);
                          } else {
                            setEditValue(rowData || null);
                          }
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        text
                        type="primary"
                        disabled={!!defaultValue?.mainPrizeList?.[index] && isStart}
                        onClick={() => {
                          if (row.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该奖品？',
                              onOk: () => {
                                // 真正删除行，而不是替换为空奖品
                                const newFormData = deepCopy(formData);
                                newFormData.mainPrizeList.splice(index, 1);
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          } else {
                            // 删除新添加的空奖品行
                            const newFormData = deepCopy(formData);
                            newFormData.mainPrizeList.splice(index, 1);
                            if (index > 0) {
                              newFormData.mainPrizeList[index - 1].status = 1;
                            }
                            setData(newFormData);
                          }
                        }}
                      >
                        删除
                      </Button>
                      {row.status === 1 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认下架该奖品？',
                              onOk: () => {
                                const newFormData = deepCopy(formData);
                                newFormData.mainPrizeList[index].status = 0;
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          下架
                        </Button>
                      )}
                      {row.status === 0 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认上架该奖品？',
                              onOk: () => {
                                const newFormData = deepCopy(formData);
                                newFormData.mainPrizeList[index].status = 1;
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          上架
                        </Button>
                      )}
                    </FormItem>
                  );
                }}
              />
            </Table>
            <Button
              type="secondary"
              style={{ marginTop: 10 }}
              onClick={() => {
                // 奖品列表为空时，直接添加一个启用的奖品
                const newFormData = deepCopy(formData);
                // 确保 mainPrizeList 存在
                if (!newFormData.mainPrizeList) {
                  newFormData.mainPrizeList = [];
                }
                newFormData.mainPrizeList.push(deepCopy(PRIZE_INFO));
                setData(newFormData);
                setTarget(newFormData.mainPrizeList.length - 1);
                setVisible(true);
              }}
            >
              添加奖品
            </Button>
          </FormItem>
          <FormItem label="兜底奖品列表" required {...formItemLayout}>
            <Table dataSource={formData.bottomPrizeList || []} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>{row.sendTotalCount ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                )}
              />
              <Table.Column
                title="中奖弹窗内奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="礼品池内奖品图"
                cell={(_, index, row) => (
                  <LzImageSelector
                    width={200}
                    height={200}
                    value={row.topShowImg}
                    onChange={(topShowImg) => {
                      const newFormData = deepCopy(formData);
                      // 确保 bottomPrizeList 存在
                      if (!newFormData.bottomPrizeList) {
                        newFormData.bottomPrizeList = [];
                      }
                      if (newFormData.bottomPrizeList[index]) {
                        newFormData.bottomPrizeList[index].topShowImg = topShowImg;
                        setData(newFormData);
                      }
                    }}
                  />
                )}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, row) => {
                  return (
                    <FormItem style={{ paddingTop: '18px' }}>
                      <Button
                        text
                        type="primary"
                        disabled={false}
                        onClick={() => {
                          const rowData = formData.bottomPrizeList?.[index];
                          if (rowData?.prizeName === '') {
                            setBottomEditValue(null);
                          } else {
                            setBottomEditValue(rowData || null);
                          }
                          setBottomTarget(index);
                          setBottomVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        text
                        type="primary"
                        disabled={!!defaultValue?.bottomPrizeList?.[index] && isStart}
                        onClick={() => {
                          if (row.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该兜底奖品？',
                              onOk: () => {
                                // 真正删除行，而不是替换为空奖品
                                const newFormData = deepCopy(formData);
                                newFormData.bottomPrizeList.splice(index, 1);
                                setData(newFormData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          } else {
                            // 删除新添加的空奖品行
                            const newFormData = deepCopy(formData);
                            newFormData.bottomPrizeList.splice(index, 1);
                            if (index > 0) {
                              newFormData.bottomPrizeList[index - 1].status = 1;
                            }
                            setData(newFormData);
                          }
                        }}
                      >
                        删除
                      </Button>
                    </FormItem>
                  );
                }}
              />
            </Table>
            {(formData.bottomPrizeList || []).length === 0 && (
              <Button
                type="secondary"
                style={{ marginTop: 10 }}
                onClick={() => {
                  // 奖品列表为空时，直接添加一个启用的奖品
                  const newFormData = deepCopy(formData);
                  // 确保 bottomPrizeList 存在
                  if (!newFormData.bottomPrizeList) {
                    newFormData.bottomPrizeList = [];
                  }
                  newFormData.bottomPrizeList.push(deepCopy(PRIZE_INFO));
                  setData(newFormData);
                  setBottomTarget(newFormData.bottomPrizeList.length - 1);
                  setBottomVisible(true);
                }}
              >
                添加兜底奖品
              </Button>
            )}
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasLimit={false}
          typeList={[1, 2, 3, 7]}
          defaultTarget={2}
          hasProbability={false}
          defaultEditValue={editValue}
        />
      </LzDialog>

      <LzDialog
        title={false}
        visible={bottomVisible}
        footer={false}
        onClose={() => setBottomVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={bottomEditValue}
          onChange={onBottomPrizeChange}
          onCancel={onBottomCancel}
          hasLimit={false}
          typeList={[4]}
          defaultTarget={4}
          hasProbability={false}
          defaultEditValue={bottomEditValue}
        />
      </LzDialog>
    </div>
  );
};
