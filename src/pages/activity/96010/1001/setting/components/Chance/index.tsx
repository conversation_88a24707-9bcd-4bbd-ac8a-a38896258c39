import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import { Form, NumberPicker, Field } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { PageData, FormLayout } from '../../../util';
import { activityEditDisabled } from '@/utils';

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data }, 'task');
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="抽奖机会">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item
            label="抽奖机会"
            required
            requiredMessage="请输入赠送次数"
            extra={<div className="next-form-item-help">注：所下订单需满足【订单限制】所设条件后才赠送抽奖机会</div>}
          >
            <div style={{ display: 'flex', lineHeight: '28px' }}>
              在下单时间内，订单每符合一次抽奖条件，赠送&nbsp;
              <Form.Item name="chance" required requiredTrigger="onBlur" requiredMessage="请输入赠送次数">
                <NumberPicker
                  min={1}
                  max={9999999}
                  type="inline"
                  value={formData.chance}
                  onChange={(chance: number) => setData({ chance })}
                />
                &nbsp;
              </Form.Item>
              次抽奖机会
            </div>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
