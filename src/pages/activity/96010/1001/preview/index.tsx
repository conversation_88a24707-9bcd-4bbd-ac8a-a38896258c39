import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import SkuList from '@/components/SkuList';
import LzImageSelector from '@/components/LzImageSelector';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="参与时段限制">
          {formData.partake === 1 ? `${formData.partakeStartTime}至${formData.partakeEndTime}` : '关闭'}
        </FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        <FormItem label="活动生成人群包">{`${formData.crowdPackage === 1 ? '开启' : '关闭'}`}</FormItem>
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderRestrainStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderRestrainEndTime)}`}</FormItem>
        <FormItem label="订单状态">{formData.orderRestrainStatus === 1 ? '已完成' : '已付款'}</FormItem>
        <FormItem label="订单笔数">
          {formData.orderStrokeStatus === 1 ? '单笔' : `大于等于${formData.orderStrokeCount}笔`}
        </FormItem>
        {/* <FormItem label="奖品延迟发放">{formData.awardDays > 0 ? `延迟发放${formData.awardDays}天` : '否'}</FormItem> */}
        <FormItem label="订单拆单后是否合并">{formData.split ? '是' : '否'}</FormItem>
        <FormItem label="价格类型">{formData.priceType === 1 ? '实付价' : '京东价'}</FormItem>
        <FormItem label="订单金额">大于等于{formData.orderRestrainAmount}元</FormItem>
        <FormItem label="订单商品">
          {formData.orderSkuisExposure === 0 && (
            <div>
              全部商品<span style={{ fontSize: '12px', color: 'gray', marginTop: '15px' }}>(不含虚拟商品)</span>
            </div>
          )}
          {formData.orderSkuisExposure === 1 && (
            <div>
              指定商品
              <SkuList skuList={formData.orderSkuList} />
            </div>
          )}
          {formData.orderSkuisExposure === 2 && (
            <div>
              排除商品
              <SkuList skuList={formData.orderSkuList} />
            </div>
          )}
        </FormItem>
        <FormItem label="奖品列表" isPreview={false}>
          <Table dataSource={formData.mainPrizeList} style={{ marginTop: '15px' }}>
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.prizeName ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => (
                <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
              )}
            />
            <Table.Column
              title="中奖弹窗内奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
            <Table.Column
              title="礼品池内奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.topShowImg} alt="" />}
            />
            <Table.Column
              title="奖品状态"
              cell={(_, index, row) => (
                <div style={{ color: row.status === 1 ? 'green' : 'red' }}>
                  {row && row.prizeName === '' ? '-' : row && row.status === 1 ? '已启用' : '已下架'}
                </div>
              )}
            />
          </Table>
        </FormItem>
        <FormItem label="兜底奖品列表" isPreview={false}>
          <Table dataSource={formData.bottomPrizeList} style={{ marginTop: '15px' }}>
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => (
                <div>{row.unitPrice ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
              )}
            />
            <Table.Column
              title="中奖弹窗内奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
            <Table.Column
              title="礼品池内奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.topShowImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="每人每天最多获奖次数">
          {formData.winLotteryDayType === 1 && '不限制'}
          {formData.winLotteryDayType === 2 && `限制每天内用户最多中奖${formData.winLotteryDayCounts}次`}
        </FormItem>
        <FormItem label="每人累计最多中奖次数">
          {formData.winLotteryTotalType === 1 && '不限制'}
          {formData.winLotteryTotalType === 2 && `限制活动周期内用户最多中奖${formData.winLotteryTotalCounts}次`}
        </FormItem>
        <FormItem label="抽奖机会">在下单时间内，订单每符合一次抽奖条件，赠送{formData.chance}次抽奖机会</FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
