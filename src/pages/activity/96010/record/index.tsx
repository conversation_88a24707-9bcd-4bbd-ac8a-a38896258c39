import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from '@/pages/activity/96010/record/components/WinRecord';
import ChanceRecord from '@/pages/activity/96010/record/components/ChanceRecord';
import LzDocGuide from '@/components/LzDocGuide';
import { getParams } from '@/utils';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title={`${getParams('name') ? getParams('name') : '伊利百分百抽盲盒'}领取记录`} actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="领奖记录" key="1">
            <WinRecord />
          </Tab.Item>
          <Tab.Item title="领奖机会记录" key="2">
            <ChanceRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
