/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Radio, Input } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';
import LzColorPicker from '@/components/LzColorPicker';

const RadioGroup = Radio.Group;

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 6,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}
export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="活动主页" subTitle="活动规则按钮，我的奖品按钮，手机号及短信验证码输入框，提交按钮，领取攻略图">
        <Form {...formLayout} className={styles.form}>
          {/* <Form.Item label="是否展示店铺名称"> */}
          {/*  <RadioGroup */}
          {/*    value={formData.disableShopName} */}
          {/*    onChange={(disableShopName: number) => { */}
          {/*      setForm({ disableShopName }); */}
          {/*    }} */}
          {/*  > */}
          {/*    <Radio id="1" value={1}> */}
          {/*      是 */}
          {/*    </Radio> */}
          {/*    <Radio id="0" value={0}> */}
          {/*      否 */}
          {/*    </Radio> */}
          {/*  </RadioGroup> */}
          {/* </Form.Item> */}
          <Form.Item label="页面背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={688}
                  height={570}
                  value={formData.qy2Bg}
                  onChange={(qy2Bg) => {
                    setForm({ qy2Bg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度688px*570px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ pageBg: defaultValue?.pageBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="领取攻略图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={718}
                  height={909}
                  value={formData.stepImg}
                  onChange={(stepImg) => {
                    setForm({ stepImg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：718px*909px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ stepImg: defaultValue?.stepImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzPanel title="不满足页KV" subTitle="上传图于 新客礼1.0不满足页，老客礼1.0不满足页进行展示">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="促销机制展示图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={640}
                  height={788}
                  value={formData.canNotJoinKv}
                  onChange={(canNotJoinKv) => {
                    setForm({ canNotJoinKv });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：640px*788px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ canNotJoinKv: defaultValue?.canNotJoinKv });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="不满足页跳转链接">
            <Input
              value={formData.jumpLink}
              onChange={(jumpLink) => {
                setForm({ jumpLink });
              }}
            />
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
