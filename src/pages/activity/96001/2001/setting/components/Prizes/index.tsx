import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import { activityEditDisabled } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO } from '../../../util';
import { PrizeInfo } from '@/pages/activity/96001/2001/util';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 进行中互动编辑资产
  const [changeVisible, setChangeVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState<{ originalPlanId: string } | null>(null);
  // 修改奖品相关参数
  const [prizeName, setPrizeName] = useState('');
  const [sendCount, setSendCount] = useState('');
  const [singleSendCount, setSingleSendCount] = useState('');
  const [unitPrice, setUnitPrice] = useState('');
  const [planId, setPlanId] = useState('');
  // 当前编辑的奖品索引
  const [currentPrizeIndex, setCurrentPrizeIndex] = useState(0);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新奖品信息
    console.log(data, '---------------------');
    formData.prizeList[0] = data;
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    const list: PrizeInfo[] = formData.prizeList.length > 0 ? [...formData.prizeList] : [PRIZE_INFO];
    setData({ prizeList: list });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  /**
   * 处理单位数量变化
   * @param value 新的单位数量
   */
  const handleSingleSendCountChange = (value: string): void => {
    setSingleSendCount(value);

    // 自动计算单份价值 = 单位数量 / 100
    if (value) {
      const singleCount = parseFloat(value) || 0;
      const calculatedUnitPrice = (singleCount / 100).toFixed(2);
      setUnitPrice(calculatedUnitPrice);
    } else {
      setUnitPrice('');
    }
  };

  /**
   * 更换奖品信息
   * @param originalPlanId 原始计划id（初始加载时的计划ID）
   * @param index 奖品索引
   */
  const handleChangePrize = (originalPlanId: string, index: number): void => {
    console.log('Debug - handleChangePrize函数中的原始计划ID:', originalPlanId);

    // 清空输入框，不回显原有数据
    setPrizeName('');
    setSendCount('');
    setSingleSendCount('');
    setUnitPrice('');
    setPlanId('');
    // 保存原始计划ID和奖品索引
    setEditValue({ originalPlanId });
    console.log('Debug - 设置后的editValue:', { originalPlanId });
    setCurrentPrizeIndex(index);
    setChangeVisible(true);
  };

  return (
    <div>
      <LzPanel title="奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="奖品列表" required>
            <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
              <Table.Column
                title="每日发放限额"
                cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, _) => (
                  <div>
                    <>
                      {activityEditDisabled() && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            const originalPlanId = formData.prizeList[index].planId;
                            handleChangePrize(originalPlanId, index);
                          }}
                        >
                          更换奖品
                        </Button>
                      )}
                    </>
                    {!activityEditDisabled() && (
                      <>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            const row = formData.prizeList[index];
                            setEditValue(row);
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            if (_.prizeType) {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认清空奖品？',
                                onOk: () => {
                                  formData.prizeList.splice(index, 1, PRIZE_INFO);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }
                          }}
                        >
                          <i className={`iconfont icon-shanchu`} />
                        </Button>
                        {formData.prizeList.length > 0 && index > 0 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.prizeList.splice(
                                index - 1,
                                1,
                                ...formData.prizeList.splice(index, 1, formData.prizeList[index - 1]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-35`} />
                          </Button>
                        )}
                        {formData.prizeList.length > 0 && index < formData.prizeList.length - 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.prizeList.splice(
                                index,
                                1,
                                ...formData.prizeList.splice(index + 1, 1, formData.prizeList[index]),
                              );
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-iconjiantou-34`} />
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                )}
              />
            </Table>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          productImgTip={false}
          hasLimit
          prizeNameLength={15}
          typeList={[2]}
          // defaultTarget={2}
        />
      </LzDialog>
      <LzDialog
        title="更换奖品"
        visible={changeVisible}
        footer={false}
        onClose={() => setChangeVisible(false)}
        style={{ width: '670px' }}
      >
        <div style={{ padding: '20px' }}>
          <Form {...formItemLayout}>
            <FormItem label="奖品名称" required>
              <Input value={prizeName} onChange={(value: any) => setPrizeName(value)} placeholder="请输入新奖品名称" />
            </FormItem>
            <FormItem label="单位数量" required>
              <Input value={singleSendCount} onChange={handleSingleSendCountChange} placeholder="请输入单位数量" />
            </FormItem>
            <FormItem label="单份价值(元)">
              <div>{unitPrice || '-'}</div>
            </FormItem>
            <FormItem label="发放份数" required>
              <Input value={sendCount} onChange={(value: any) => setSendCount(value)} placeholder="请输入新发放份数" />
            </FormItem>
            <FormItem label="计划ID" required>
              <Input value={planId} onChange={(value: any) => setPlanId(value)} placeholder="请输入新计划ID" />
            </FormItem>
            <FormItem wrapperCol={{ offset: 4 }}>
              <Button
                type="primary"
                onClick={() => {
                  // 验证必填项
                  if (!prizeName || !sendCount || !singleSendCount || !planId) {
                    Dialog.alert({
                      title: '提示',
                      content: '请填写完整的奖品信息',
                      onOk: () => {},
                    });
                    return;
                  }

                  // 保存奖品信息
                  const currentPrize = formData.prizeList[currentPrizeIndex];
                  if (currentPrize) {
                    currentPrize.prizeName = prizeName;
                    currentPrize.sendTotalCount = sendCount;
                    // 单位数量存入numPerSending和unitCount字段
                    currentPrize.numPerSending = singleSendCount;
                    currentPrize.unitCount = singleSendCount;
                    // 单份价值
                    currentPrize.unitPrice = unitPrice;
                    currentPrize.planId = planId;
                    currentPrize.prizeKey = planId;
                    setData({ ...formData });
                  }
                  setChangeVisible(false);
                }}
              >
                确定
              </Button>
              <Button style={{ marginLeft: '10px' }} onClick={() => setChangeVisible(false)}>
                取消
              </Button>
            </FormItem>
          </Form>
        </div>
      </LzDialog>
    </div>
  );
};
