import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface ItemInfo {
  num: string;
  title: string;
  type: number;
}

export interface CustomValue {
  actBg: string;
  shopNameColor: string;
  actBgColor: string;
  btnColor: string;
  prizeListImg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  disableShopName: number;
}

export interface PageData {
  shopName: string;
  rangeDate: [dayjs.Dayjs, dayjs.Dayjs];
  gradeLabel: any[];
  h5Img: string;
  activityName: string;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  threshold: number;
  rules: string;
  templateCode: string;
  itemList: any[];
  endActivity: number;
  shareStatus: number;
  shareTitle: string;
  mpImg: string;
  prizeList: any[];
  cmdImg: string;
  startTime: string;
  endTime: string;
  crowdBag: any;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  prizeListImg: '',
  disableShopName: 0,
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `完善信息有礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 奖品列表
    prizeList: [],
    // 题目标题列表
    // 题目标题列表
    itemList: [
      {
        num: '1',
        title: '姓名',
        type: 0,
      },
      {
        num: '3',
        title: '电话',
        type: 0,
      },
      {
        num: '4',
        title: '性别',
        type: 0,
      },
      {
        num: '2',
        title: '生日',
        type: 0,
      },
      {
        num: '6',
        title: '地址',
        type: 0,
      },
      {
        num: '7',
        title: '详细地址',
        type: 0,
        disabled: true,
      },
    ],
    // 活动强制结束
    endActivity: 0,
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '完善信息，领取好礼！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 2,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
};

// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  console.log(prize, '0-----------------------------------');
  const type = prize.prizeType;
  // 3-实物 4-积分 没有时间直接通过
  if (type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};
const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  if (prizeList[0].prizeName === '') {
    Message.error('请设置奖品');
    return false;
  }
  return true;
};
// const [isEmpty, setIsEmpty] = useState(false);
const hasItem = (itemList: ItemInfo[]): boolean => {
  for (let i = 0; i < itemList.length; i++) {
    if (itemList[i].type === 1 && itemList[i].title === '') {
      Message.error('请完善信息条目');
      return false;
    }
  }
  if (!itemList.length) {
    Message.error('请至少添加1个完善信息条目');
    return false;
  }
  return true;
};

export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择奖品
  if (!hasPrize(prizeList)) {
    return false;
  }
  // 没有设置信息条目
  if (!hasItem(formData.itemList)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(prizeList, formData)) {
    return false;
  }
  return true;
};
