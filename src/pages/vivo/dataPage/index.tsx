import React, { useEffect, useReducer, useState } from 'react';
import { Button, DatePicker2, Field, Form, Table } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import { x300DataPage, x300ReportExport } from '@/api/vivo';
import LzPanel from '@/components/LzPanel';
import { downloadExcel } from '@/utils';
import format from '@/utils/format';
import dayjs from 'dayjs';
import LzPagination, { Pager } from '@/components/LzPagination';
import { FormLayout } from '@/types/Form';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const initPager: Pager = {
  pageNo: 1,
  pageSize: 10,
  total: 0,
};

export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(true); // true展示好剧不断  false会员礼遇
  const [list, setList] = useState<any>({});
  const [listData, setListData] = useState<any>([]);
  const [tableHeader, setTableHeader] = useState<any>([
    {
      value: '日期',
      key: 'dt',
    },
    {
      value: 'PV',
      key: 'pv',
    },
    {
      value: 'UV',
      key: 'uv',
    },
    {
      value: '加购商品完成人数',
      key: 'addSkuPeople',
    },
    {
      value: '预约新品完成完成人数',
      key: 'appointSkuPeople',
    },
    {
      value: '浏览会场完成完成人数',
      key: 'browseAreaPeople',
    },
    {
      value: '搜索新品完成完成人数',
      key: 'browseSearchPeople',
    },
    {
      value: '浏览商品完成人数',
      key: 'browseSkuPeople',
    },
    {
      value: 'POP开卡入会完成人数',
      key: 'openCardPeople',
    },
    {
      value: '下单商品完成人次数',
      key: 'orderCount',
    },
    {
      value: '下单商品完成人数',
      key: 'orderPeople',
    },
    {
      value: '签到人数',
      key: 'signPeople',
    },
    {
      value: '自营开卡入会完成人数',
      key: 'soOpenCardPeople',
    }
  ]);
  // 请求数据
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    startTime: format.formatDateTimeDayjs(dayjs().startOf('month'), 'YYYY-MM-DD'),
    endTime: format.formatDateTimeDayjs(dayjs(), 'YYYY-MM-DD'),
    ...initPager,
  });
  // 活动
  const [activity, setActivity] = useState<any>('');

  const rangePreset = {
    上个月: [dayjs().add(-1, 'month').startOf('month'), dayjs().add(-1, 'month').endOf('month')],
    本月: [dayjs().startOf('month'), dayjs().endOf('month')],
    下个月: [dayjs().add(+1, 'month').startOf('month'), dayjs().add(+1, 'month').endOf('month')],
    昨天: [dayjs().add(-1, 'day'), dayjs()],
    今天: [dayjs(), dayjs().add(1, 'day')],
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setParams({
      startTime: format.formatDateTimeDayjs(rangeDate[0], 'YYYY-MM-DD'),
      endTime: format.formatDateTimeDayjs(rangeDate[1], 'YYYY-MM-DD'),
      // activityId: getParams('id'),
    });
  };

  const handleSubmit = () => {
    setList({});
    setListData([]);
    setParams({ ...params });
    getList({ ...params }).then();
  };
  const handleReset = () => {
    setList({});
    setListData([]);
    setParams({
      startTime: format.formatDateTimeDayjs(dayjs().startOf('month'), 'YYYY-MM-DD'),
      endTime: format.formatDateTimeDayjs(dayjs(), 'YYYY-MM-DD'),
      pageNo: 1,
      pageSize: params.pageSize,
    });
    getList({
      ...params,
    }).then();
  };
  const exportfn = () => {
    x300ReportExport({
      startTime: params.startTime,
      endTime: params.endTime,
    }).then((data: any) => downloadExcel(data, ``));
  };
  const getList = async (query) => {
    const postData = {
      ...params,
      ...query,
    };
    console.log('*************************', postData);
    delete postData.total;
    try {
      setLoading(true);
      let result: any = {};
      result = await x300DataPage(postData);
      setListData(result.records);
      const info = {
        total: result.total,
        pageNo: result.current,
        pageSize: result.size,
      };
      setParams({ ...params, ...info });
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    setList({});
    setListData([]);
    getList({ ...params }).then();
  }, []);

  const currentDate = dayjs('2024-07-01');

  const disabledDate = function (date, mode) {
    switch (mode) {
      case 'date':
        return date.valueOf() <= currentDate.valueOf();
      case 'year':
        return date.year() < currentDate.year();
      case 'month':
        return date.year() * 100 + date.month() < currentDate.year() * 100 + currentDate.month();
      default:
        return false;
    }
  };

  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem name="dateRange" label="选择时间">
            <RangePicker
              format="YYYY-MM-DD"
              // preset={rangePreset}
              value={[params.startTime, params.endTime]}
              hasClear={false}
              disabledDate={disabledDate}
              onChange={(value) => {
                onDataRangeChange(value);
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
            <Form.Reset
              onClick={() => {
                handleReset();
              }}
            >
              重置
            </Form.Reset>
            <Button type="primary" onClick={exportfn}>
              导出
            </Button>
          </FormItem>
        </Form>
      </LzPanel>
      <LzPanel>
        <Table.StickyLock dataSource={listData} loading={loading}>
          {tableHeader.map((head, index) => {
            return <Table.Column key={index} width={150} title={head.value} dataIndex={head.key} />;
          })}
        </Table.StickyLock>
        <LzPagination
          total={params.total}
          pageNum={params.pageNo}
          pageSize={params.pageSize}
          onChange={(e) => {
            console.log(e);
            const info = {
              pageNo: e.pageNum,
              pageSize: e.pageSize,
            };
            setParams({ ...params, ...info });
            getList({
              ...{
                pageNo: e.pageNum,
                pageSize: e.pageSize,
              },
            }).then();
          }}
        />
      </LzPanel>
    </div>
  );
};
