import React, { useEffect, useReducer, useState } from 'react';
import { Button, Field, Form, Table, Input, Select, Upload, DatePicker2 } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import {
  modelCompareGetCategory,
  modelCompareSearch,
  modelCompareTemplateExport,
  modelCompareDataGetEstateDataPage,
  modelCompareDataEstateDataExport,
  modelCompareDataEstateAllDataExport,
} from '@/api/haier';
import { HaierModelCompareCategoryResponse, TemplateExportUsingPostParams } from '@/api/types';
import LzPanel from '@/components/LzPanel';
import LzPagination from '@/components/LzPagination';
import { FormLayout } from '@/types/Form';
import { downloadExcel } from '@/utils';
import { getShop } from '@/utils/shopUtil';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface pager {
  pageNum: number;
  pageSize: number;
  total: number;
}

const initPager: pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const Option = Select.Option;
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [categoryList, setCategoryList] = useState<HaierModelCompareCategoryResponse[]>([]);
  const [categoryId, setCategoryId] = useState<string>('');
  const [subCategoryList, setSubCategoryList] = useState<HaierModelCompareCategoryResponse[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [listData, setListData] = useState<any>([]);
  const [columns, setColumns] = useState<any>([]);
  const [uploaderRef, setUploaderRef] = useState(false);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<pager>(initPager);
  // 根据shopId判断activityId的值
  const getActivityId = () => {
    const shopId = getShop().shopId;
    if (shopId === 1000004489) {
      return '1960158584593752066';
    } else if (shopId === 1000001782) {
      return '1957726574521032706';
    }
    return '';
  };
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    activityId: getActivityId(),
    categoryId: '',
    startTime: '',
    endTime: '',
    ...initPager,
  });

  // 获取类目
  const getCategory = async () => {
    try {
      const res = await modelCompareGetCategory();
      setCategoryList(res || []);
    } catch (error) {
      console.error('获取类目失败:', error);
    }
  };

  // 一级类目选择变化
  const onCategoryChange = (value: string) => {
    setSelectedCategoryId(value);
    field.setValue('subCategoryId', ''); // 清空二级选择
    // 查找选中类目的子类目
    const selectedCategory = categoryList.find((item) => item.id === value);
    if (selectedCategory && selectedCategory.childList && selectedCategory.childList.length > 0) {
      setSubCategoryList(selectedCategory.childList);
      // 不需要校验二级类目，直接使用一级类目ID
      setCategoryId(value);
      setParams({
        categoryId: value,
      });
    } else {
      setSubCategoryList([]);
      // 没有子类目时，直接将父类目ID赋值给categoryId
      setCategoryId(value);
      setParams({
        categoryId: value,
      });
    }
  };

  // 二级类目选择变化
  const onSubCategoryChange = (value: string) => {
    // 选择二级类目时，将二级类目ID赋值给categoryId
    setCategoryId(value);
    setParams({
      categoryId: value,
    });
  };
  const getList = async (query: any) => {
    const postData = {
      ...params,
      ...query,
    };
    try {
      setLoading(true);
      const res = await modelCompareDataGetEstateDataPage({ ...postData });
      // 处理接口返回的数据
      if (res) {
        setListData((res.records as any) || []);
        setPageInfo({
          total: Number(res?.total || 0),
          pageNum: Number(res?.current || 1),
          pageSize: Number(res?.size || 10),
        });
      }
    } catch (e: any) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const handleSubmit = async () => {
    const formData: any = field.getValues();
    // 校验日期不能为空
    if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
      LzMsg.error('请选择日期范围');
      return;
    }
    const newParams = {
      categoryId: categoryId,
      startTime: formData.dateRange[0].format('YYYY-MM-DD'),
      endTime: formData.dateRange[1].format('YYYY-MM-DD'),
      ...initPager,
    };
    setParams(newParams);
    getList(newParams);
  };
  // 导出
  const exportfn = () => {
    try {
      const formData: any = field.getValues();
      if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
        LzMsg.error('请选择日期范围');
        return;
      }
      const dateRange = `${params.startTime}_${params.endTime}`;
      modelCompareDataEstateDataExport({ ...params })
        .then((data: any) => downloadExcel(data, `产业数据报表_${dateRange}`))
        .catch((error) => {
          LzMsg.error(error.message);
        });
    } catch (error) {
      LzMsg.error(error.message);
    }
  };
  // 全量导出
  const exportAllfn = () => {
    try {
      const formData: any = field.getValues();
      if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
        LzMsg.error('请选择日期范围');
        return;
      }
      const dateRange = `${params.startTime}_${params.endTime}`;
      modelCompareDataEstateAllDataExport({ ...params })
        .then((data: any) => downloadExcel(data, `全部产业数据报表_${dateRange}`))
        .catch((error) => {
          LzMsg.error(error.message);
        });
    } catch (error) {
      LzMsg.error(error.message);
    }
  };
  useEffect(() => {
    getCategory();
  }, []);

  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem name="categoryId" label="一级类目">
            <Select placeholder="请选择一级类目" onChange={onCategoryChange} style={{ marginRight: 8, width: 200 }}>
              {categoryList.map((item) => (
                <Option key={item.id} value={item.id!}>
                  {item.categoryName}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem name="subCategoryId" label="二级类目">
            <Select
              placeholder="请先选择一级类目"
              onChange={onSubCategoryChange}
              disabled={!selectedCategoryId}
              style={{ marginRight: 8, width: 200 }}
            >
              {subCategoryList.map((item) => (
                <Option key={item.id} value={item.id!}>
                  {item.categoryName}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem name="dateRange" label="日期">
            <RangePicker
              format="YYYY-MM-DD"
              style={{ width: 150 }}
              onChange={(value) => {
                if (value && value.length === 2) {
                  // 更新参数中的日期
                  setParams({
                    ...params,
                    startTime: value[0].format('YYYY-MM-DD'),
                    endTime: value[1].format('YYYY-MM-DD'),
                  });
                }
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
            <Button onClick={exportfn}>导出</Button>
            <Button onClick={exportAllfn}>全量导出</Button>
          </FormItem>
        </Form>
      </LzPanel>

      <LzPanel>
        <Table.StickyLock dataSource={listData} loading={loading}>
          <Table.Column align="center" title="日期" dataIndex="dt" />
          <Table.Column align="center" title="产业" dataIndex="series" />
          <Table.Column align="center" title="二级类目" dataIndex="estateName" />
          <Table.Column align="center" title="PV" dataIndex="pv" />
          <Table.Column align="center" title="UV" dataIndex="uv" />
          <Table.Column align="center" title="下单人数" dataIndex="totalBuyers" />
          <Table.Column align="center" title="下单GMV" dataIndex="totalGmv" />
          <Table.Column align="center" title="成交人数" dataIndex="payBuyers" />
          <Table.Column align="center" title="成交GMV" dataIndex="payGmv" />
          <Table.Column align="center" title="转化率" dataIndex="orderRate" />
        </Table.StickyLock>
        <LzPagination
          total={pageInfo.total}
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          onChange={(e) => {
            const newParams = {
              ...params,
              pageNum: e.pageNum,
              pageSize: e.pageSize,
            };
            setParams(newParams);
            getList({
              ...newParams,
            });
          }}
        />
      </LzPanel>
    </div>
  );
};
