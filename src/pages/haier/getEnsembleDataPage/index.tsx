import React, { useEffect, useReducer, useState } from 'react';
import { Button, Field, Form, Table, Input, Select, Upload, DatePicker2 } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import { modelCompareDataGetEnsembleDataPage, modelCompareDataEnsembleDataExport } from '@/api/haier';
import LzPanel from '@/components/LzPanel';
import LzPagination from '@/components/LzPagination';
import { FormLayout } from '@/types/Form';
import { downloadExcel } from '@/utils';
import { getShop } from '@/utils/shopUtil';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface pager {
  pageNum: number;
  pageSize: number;
  total: number;
}

const initPager: pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [categoryId, setCategoryId] = useState<string>('');
  const [listData, setListData] = useState<any>([]);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<pager>(initPager);
  // 根据shopId判断activityId的值
  const getActivityId = () => {
    const shopId = getShop().shopId;
    if (shopId === 1000004489) {
      return '1960158584593752066';
    } else if (shopId === 1000001782) {
      return '1957726574521032706';
    }
    return '';
  };
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    activityId: getActivityId(),
    startTime: '',
    endTime: '',
    ...initPager,
  });
  const getList = async (query: any) => {
    const postData = {
      ...params,
      ...query,
    };
    try {
      setLoading(true);
      const res = await modelCompareDataGetEnsembleDataPage({ ...postData });
      // 处理接口返回的数据
      if (res) {
        setListData(res?.records || []);
        setPageInfo({
          total: Number(res?.total || 0),
          pageNum: Number(res?.current || 1),
          pageSize: Number(res?.size || 10),
        });
      }
    } catch (e: any) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  // 查询
  const handleSubmit = () => {
    const formData: any = field.getValues();
    // 校验日期不能为空
    if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
      LzMsg.error('请选择日期范围');
      return;
    }
    const newParams = {
      startTime: formData.dateRange[0].format('YYYY-MM-DD'),
      endTime: formData.dateRange[1].format('YYYY-MM-DD'),
    };
    setParams(newParams);
    getList({
      ...newParams,
    });
  };
  // 导出
  const exportfn = () => {
    const formData: any = field.getValues();
    const newParams = {
      startTime: formData.dateRange[0].format('YYYY-MM-DD'),
      endTime: formData.dateRange[1].format('YYYY-MM-DD'),
    };
    try {
      const dateRange = `${newParams.startTime}_${newParams.endTime}`;
      modelCompareDataEnsembleDataExport({ ...params, ...newParams })
        .then((data: any) => downloadExcel(data, `整体数据报表_${dateRange}`))
        .catch((error) => {
          LzMsg.error(error.message);
        });
    } catch (error) {
      LzMsg.error(error.message);
    }
  };
  useEffect(() => {}, []);

  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem name="dateRange" label="日期">
            <RangePicker
              format="YYYY-MM-DD"
              style={{ width: 150 }}
              onChange={(value) => {
                if (value && value.length === 2) {
                  // 更新参数中的日期
                  setParams({
                    ...params,
                    startTime: value[0].format('YYYY-MM-DD'),
                    endTime: value[1].format('YYYY-MM-DD'),
                  });
                }
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
            <Button onClick={exportfn}>导出</Button>
          </FormItem>
        </Form>
      </LzPanel>

      <LzPanel>
        <Table dataSource={listData} loading={loading}>
          <Table.Column align="center" title="日期" dataIndex="dt" />
          <Table.Column align="center" title="PV" dataIndex="pv" />
          <Table.Column align="center" title="UV" dataIndex="uv" />
          <Table.Column align="center" title="下单人数" dataIndex="totalBuyers" />
          <Table.Column align="center" title="下单GMV" dataIndex="totalGmv" />
          <Table.Column align="center" title="成交人数" dataIndex="payBuyers" />
          <Table.Column align="center" title="成交GMV" dataIndex="payGmv" />
          <Table.Column align="center" title="转化率" dataIndex="orderRate" />
        </Table>
        <LzPagination
          total={pageInfo.total}
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          onChange={(e) => {
            // const formData: any = field.getValues();
            const newParams = {
              // startTime: formData.dateRange[0].format('YYYY-MM-DD'),
              // endTime: formData.dateRange[1].format('YYYY-MM-DD'),
              ...params,
              pageNum: e.pageNum,
              pageSize: e.pageSize,
            };
            setParams(newParams);
            getList({ ...newParams });
          }}
        />
      </LzPanel>
    </div>
  );
};
