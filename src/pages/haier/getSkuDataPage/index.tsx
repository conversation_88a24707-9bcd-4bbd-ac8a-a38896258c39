import React, { useEffect, useReducer, useState } from 'react';
import { Button, Field, Form, Table, Input, Select, Upload, DatePicker2 } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import { modelCompareDataGetSkuDataPage, modelCompareDataSkuDataExport, modelCompareGetCategory } from '@/api/haier';
import { HaierModelCompareCategoryResponse, TemplateExportUsingPostParams } from '@/api/types';
import LzPanel from '@/components/LzPanel';
import LzPagination from '@/components/LzPagination';
import { FormLayout } from '@/types/Form';
import { downloadExcel } from '@/utils';
import { getShop } from '@/utils/shopUtil';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface pager {
  pageNum: number;
  pageSize: number;
  total: number;
}

const initPager: pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const Option = Select.Option;
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [categoryList, setCategoryList] = useState<HaierModelCompareCategoryResponse[]>([]);
  const [categoryId, setCategoryId] = useState<string>('');
  const [subCategoryList, setSubCategoryList] = useState<HaierModelCompareCategoryResponse[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [listData, setListData] = useState<any>([]);
  const [columns, setColumns] = useState<any>([]);
  const [uploaderRef, setUploaderRef] = useState(false);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<pager>(initPager);
  // 根据shopId判断activityId的值
  const getActivityId = () => {
    const shopId = getShop().shopId;
    if (shopId === 1000004489) {
      return '1960158584593752066';
    } else if (shopId === 1000001782) {
      return '1957726574521032706';
    }
    return '';
  };
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    activityId: getActivityId(),
    categoryId: '',
    skuIds: '',
    startTime: '',
    endTime: '',
    ...initPager,
  });

  // 获取类目
  const getCategory = async () => {
    try {
      const res = await modelCompareGetCategory();
      setCategoryList(res || []);
    } catch (error) {
      console.error('获取类目失败:', error);
    }
  };

  // 一级类目选择变化
  const onCategoryChange = (value: string) => {
    setSelectedCategoryId(value);
    field.setValue('subCategoryId', ''); // 清空二级选择
    // 查找选中类目的子类目
    const selectedCategory = categoryList.find((item) => item.id === value);
    if (selectedCategory && selectedCategory.childList && selectedCategory.childList.length > 0) {
      setSubCategoryList(selectedCategory.childList);
      // 不需要校验二级类目，直接使用一级类目ID
      setCategoryId(value);
      setParams({
        categoryId: value,
      });
    } else {
      setSubCategoryList([]);
      // 没有子类目时，直接将父类目ID赋值给categoryId
      setCategoryId(value);
      setParams({
        categoryId: value,
      });
    }
  };

  // 二级类目选择变化
  const onSubCategoryChange = (value: string) => {
    // 选择二级类目时，将二级类目ID赋值给categoryId
    setCategoryId(value);
    setParams({
      categoryId: value,
    });
  };
  const getList = async (query: any) => {
    const postData = {
      ...params,
      ...query,
    };
    try {
      setLoading(true);
      const res = await modelCompareDataGetSkuDataPage({ ...postData });
      // 处理接口返回的数据
      if (res) {
        setListData((res.records as any) || []);
        setPageInfo({
          total: Number(res?.total || 0),
          pageNum: Number(res?.current || 1),
          pageSize: Number(res?.size || 10),
        });
      }
    } catch (e: any) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const handleSubmit = async () => {
    const formData: any = field.getValues();
    // 校验日期不能为空
    if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
      LzMsg.error('请选择日期范围');
      return;
    }
    // 校验SKU ID格式
    if (formData.skuIds) {
      // 检查是否包含中文逗号
      if (formData.skuIds.includes('，')) {
        LzMsg.error('请使用英文逗号(,)分隔多个SKU ID');
        return;
      }
      // 检查格式：允许数字、字母、下划线、连字符，用英文逗号分隔
      const skuPattern = /^[a-zA-Z0-9_-]+(,[a-zA-Z0-9_-]+)*$/;
      if (!skuPattern.test(formData.skuIds.trim())) {
        LzMsg.error('SKU ID格式不正确，请使用英文逗号分隔');
        return;
      }
    }
    const newParams = {
      categoryId: categoryId,
      skuIds: formData.skuIds,
      startTime: formData.dateRange[0].format('YYYY-MM-DD'),
      endTime: formData.dateRange[1].format('YYYY-MM-DD'),
      ...initPager,
    };
    setParams(newParams);
    getList(newParams);
  };
  // 导出
  const exportfn = () => {
    try {
      const formData: any = field.getValues();
      if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
        LzMsg.error('请选择日期范围');
        return;
      }
      modelCompareDataSkuDataExport({ ...params })
        .then((data: any) => {
          // 添加所选日期跨度到文件名
          const dateRange = `${params.startTime}_${params.endTime}`;
          downloadExcel(data, `SKU数据报表_${dateRange}`);
        })
        .catch((error) => {
          LzMsg.error(error.message);
        });
    } catch (error) {
      LzMsg.error(error.message);
    }
  };
  useEffect(() => {
    getCategory();
  }, []);

  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem name="categoryId" label="一级类目">
            <Select placeholder="请选择一级类目" onChange={onCategoryChange} style={{ marginRight: 8, width: 200 }}>
              {categoryList.map((item) => (
                <Option key={item.id} value={item.id!}>
                  {item.categoryName}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem name="subCategoryId" label="二级类目">
            <Select
              placeholder="请先选择一级类目"
              onChange={onSubCategoryChange}
              disabled={!selectedCategoryId}
              style={{ marginRight: 8, width: 200 }}
            >
              {subCategoryList.map((item) => (
                <Option key={item.id} value={item.id!}>
                  {item.categoryName}
                </Option>
              ))}
            </Select>
          </FormItem>
          <FormItem name="skuIds" label="SKUID">
            <Input
              placeholder="多个SKU时请用英文逗号隔开"
              onChange={(value) => {
                // 实时校验SKU ID格式
                if (value) {
                  // 检查是否包含中文逗号
                  if (value.includes('，')) {
                    LzMsg.error('请使用英文逗号(,)分隔多个SKUID');
                    return;
                  }
                  // 检查格式：允许数字、字母、下划线、连字符，用英文逗号分隔
                  const skuPattern = /^[a-zA-Z0-9_-]+(,[a-zA-Z0-9_-]+)*$/;
                  if (!skuPattern.test(value.trim())) {
                    LzMsg.error('SKUID格式不正确，请使用英文逗号分隔');
                  }
                }
              }}
            />
          </FormItem>
          <FormItem name="dateRange" label="日期">
            <RangePicker
              format="YYYY-MM-DD"
              style={{ width: 150 }}
              onChange={(value) => {
                if (value && value.length === 2) {
                  // 更新参数中的日期
                  setParams({
                    ...params,
                    startTime: value[0].format('YYYY-MM-DD'),
                    endTime: value[1].format('YYYY-MM-DD'),
                  });
                }
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
            <Button onClick={exportfn}>导出</Button>
          </FormItem>
        </Form>
      </LzPanel>

      <LzPanel>
        <Table dataSource={listData} loading={loading}>
          <Table.Column align="center" title="日期" dataIndex="dt" />
          <Table.Column align="center" title="产业" dataIndex="series" />
          <Table.Column align="center" title="二级类目" dataIndex="estateName" />
          <Table.Column align="center" title="SKU" dataIndex="skuId" />
          <Table.Column align="center" title="点击次数" dataIndex="clickCount" />
          <Table.Column align="center" title="点击人数" dataIndex="clickPeople" />
          <Table.Column align="center" title="下单人数" dataIndex="totalBuyers" />
          <Table.Column align="center" title="下单GMV" dataIndex="totalGmv" />
          <Table.Column align="center" title="成交人数" dataIndex="payBuyers" />
          <Table.Column align="center" title="成交GMV" dataIndex="payGmv" />
          <Table.Column align="center" title="转化率" dataIndex="orderRate" />
        </Table>
        <LzPagination
          total={pageInfo.total}
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          onChange={(e) => {
            // const formData: any = field.getValues();
            const newParams = {
              // startTime: formData.dateRange[0].format('YYYY-MM-DD'),
              // endTime: formData.dateRange[1].format('YYYY-MM-DD'),
              ...params,
              pageNum: e.pageNum,
              pageSize: e.pageSize,
            };
            setParams(newParams);
            getList({ ...newParams });
          }}
        />
      </LzPanel>
    </div>
  );
};
