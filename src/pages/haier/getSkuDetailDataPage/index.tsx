import React, { useEffect, useReducer, useState } from 'react';
import { Button, Field, Form, Table, Input, Select, Upload, DatePicker2 } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import {
  modelCompareGetCategory,
  modelCompareSearch,
  modelCompareTemplateExport,
  modelCompareDataGetSkuDetailDataPage,
  modelCompareDataSkuDetailDataExport,
  modelCompareDataSkuDetailAllDataExport,
} from '@/api/haier';
import { HaierModelCompareCategoryResponse, TemplateExportUsingPostParams } from '@/api/types';
import LzPanel from '@/components/LzPanel';
import LzPagination from '@/components/LzPagination';
import { FormLayout } from '@/types/Form';
import { downloadExcel } from '@/utils';
import { config } from 'ice';
import CONST from '@/utils/constant';
import { getShop } from '@/utils/shopUtil';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface pager {
  pageNum: number;
  pageSize: number;
  total: number;
}

const initPager: pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const Option = Select.Option;
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [categoryList, setCategoryList] = useState<HaierModelCompareCategoryResponse[]>([]);
  const [categoryId, setCategoryId] = useState<string>('');
  const [subCategoryList, setSubCategoryList] = useState<HaierModelCompareCategoryResponse[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [listData, setListData] = useState<any>([]);
  const [columns, setColumns] = useState<any>([]);
  const [uploaderRef, setUploaderRef] = useState(false);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<pager>(initPager);
  // 根据shopId判断activityId的值
  const getActivityId = () => {
    const shopId = getShop().shopId;
    if (shopId === 1000004489) {
      return '1960158584593752066';
    } else if (shopId === 1000001782) {
      return '1957726574521032706';
    }
    return '1960158584593752066';
  };
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    activityId: getActivityId(),
    categoryId: '',
    skuIds: '',
    startTime: '',
    endTime: '',
    ...initPager,
  });

  // 获取类目
  const getCategory = async () => {
    try {
      const res = await modelCompareGetCategory();
      setCategoryList(res || []);
    } catch (error) {
      console.error('获取类目失败:', error);
    }
  };
  // 一级类目选择变化
  const onCategoryChange = (value: string) => {
    setSelectedCategoryId(value);
    field.setValue('subCategoryId', ''); // 清空二级选择
    // 查找选中类目的子类目
    const selectedCategory = categoryList.find((item) => item.id === value);
    if (selectedCategory && selectedCategory.childList && selectedCategory.childList.length > 0) {
      setSubCategoryList(selectedCategory.childList);
      // 不需要校验二级类目，直接使用一级类目ID
      setCategoryId(value);
      setParams({
        categoryId: value,
      });
    } else {
      setSubCategoryList([]);
      // 没有子类目时，直接将父类目ID赋值给categoryId
      setCategoryId(value);
      setParams({
        categoryId: value,
      });
    }
  };

  // 二级类目选择变化
  const onSubCategoryChange = (value: string) => {
    // 选择二级类目时，将二级类目ID赋值给categoryId
    setCategoryId(value);
    setParams({
      categoryId: value,
    });
  };
  const getList = async (query: any) => {
    const postData = {
      ...params,
      ...query,
    };
    try {
      setLoading(true);
      const res = await modelCompareDataGetSkuDetailDataPage({ ...postData });

      if (res) {
        const { dates, pages } = res;

        // 生成动态列配置
        const dynamicColumns: any[] = [
          {
            title: '产业',
            dataIndex: 'series',
            key: 'series',
            width: 200,
            lock: 'left',
            align: 'center',
          },
          {
            title: '二级类目',
            dataIndex: 'estateName',
            key: 'estateName',
            width: 200,
            lock: 'left',
            align: 'center',
          },
          {
            title: 'SKU对比',
            dataIndex: 'skuPair',
            key: 'skuPair',
            width: 200,
            lock: 'left',
            align: 'center',
          },
        ];

        // 为每个日期生成列配置
        dates?.forEach((date: string) => {
          dynamicColumns.push({
            title: date,
            key: date,
            width: 150,
            align: 'center',
            children: [
              {
                title: '对比人数',
                dataIndex: `${date}_comparePeople`,
                key: `${date}_comparePeople`,
                width: 75,
                align: 'center',
              },
              {
                title: '对比次数',
                dataIndex: `${date}_compareCount`,
                key: `${date}_compareCount`,
                width: 75,
                align: 'center',
              },
            ],
          });
        });

        setColumns(dynamicColumns);

        // 转换数据结构
        const transformedData = pages?.records?.map((record: any) => {
          const rowData: any = {
            series: record.series,
            estateName: record.estateName,
            skuPair: record.skuPair,
          };

          // 为每个日期的数据创建对应的字段
          record.data.forEach((item: any) => {
            rowData[`${item.dt}_comparePeople`] = item.comparePeople;
            rowData[`${item.dt}_compareCount`] = item.compareCount;
          });

          return rowData;
        });

        setListData(transformedData);
        setPageInfo({
          total: Number(pages?.total || 0),
          pageNum: Number(pages?.current || 1),
          pageSize: Number(pages?.size || 10),
        });
      }
    } catch (e: any) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const handleSubmit = async () => {
    const formData: any = field.getValues();
    // 校验SKU ID格式
    if (formData.skuIds) {
      // 检查是否包含中文逗号
      if (formData.skuIds.includes('，')) {
        LzMsg.error('请使用英文逗号(,)分隔多个SKU ID');
        return;
      }
      // 检查格式：允许数字、字母、下划线、连字符，用英文逗号分隔
      const skuPattern = /^[a-zA-Z0-9_-]+(,[a-zA-Z0-9_-]+)*$/;
      if (!skuPattern.test(formData.skuIds.trim())) {
        LzMsg.error('SKU ID格式不正确，请使用英文逗号分隔');
        return;
      }
    }
    // 校验日期不能为空
    if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
      LzMsg.error('请选择日期范围');
      return;
    }
    const newParams = {
      categoryId: categoryId,
      skuIds: formData.skuIds,
      startTime: formData.dateRange[0].format('YYYY-MM-DD'),
      endTime: formData.dateRange[1].format('YYYY-MM-DD'),
      ...initPager,
    };
    setParams(newParams);
    getList(newParams);
  };
  // 导出
  const exportfn = () => {
    try {
      // 检查日期参数是否存在
      if (!params.startTime || !params.endTime) {
        const formData: any = field.getValues();
        if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
          LzMsg.error('请选择日期范围');
          return;
        }
        // 更新参数中的日期
        setParams({
          ...params,
          startTime: formData.dateRange[0].format('YYYY-MM-DD'),
          endTime: formData.dateRange[1].format('YYYY-MM-DD'),
        });
      }

      modelCompareDataSkuDetailDataExport({ ...params })
        .then((data: any) => {
          // 添加所选日期跨度到文件名
          const dateRange = `${params.startTime}_${params.endTime}`;
          downloadExcel(data, `SKU对比数据报表_${dateRange}`);
        })
        .catch((error) => {
          LzMsg.error(error.message);
        });
    } catch (error) {
      LzMsg.error(error.message);
    }
  };
  // 全量导出
  const exportAllfn = () => {
    try {
      // 检查日期参数是否存在
      if (!params.startTime || !params.endTime) {
        const formData: any = field.getValues();
        if (!formData.dateRange || !formData.dateRange[0] || !formData.dateRange[1]) {
          LzMsg.error('请选择日期范围');
          return;
        }
        // 更新参数中的日期
        setParams({
          ...params,
          startTime: formData.dateRange[0].format('YYYY-MM-DD'),
          endTime: formData.dateRange[1].format('YYYY-MM-DD'),
        });
      }

      modelCompareDataSkuDetailAllDataExport({ ...params })
        .then((data: any) => {
          // 添加所选日期跨度到文件名
          const dateRange = `${params.startTime}_${params.endTime}`;
          downloadExcel(data, `全部SKU对比数据报表_${dateRange}`);
        })
        .catch((error) => {
          LzMsg.error(error.message);
        });
    } catch (error) {
      LzMsg.error(error.message);
    }
  };
  useEffect(() => {
    getCategory();
  }, []);

  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem name="categoryId" label="一级类目">
            <Select placeholder="请选择一级类目" onChange={onCategoryChange} style={{ marginRight: 8, width: 200 }}>
              {categoryList.map((item) => (
                <Option key={item.id} value={item.id!}>
                  {item.categoryName}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem name="subCategoryId" label="二级类目">
            <Select
              placeholder="请先选择一级类目"
              onChange={onSubCategoryChange}
              disabled={!selectedCategoryId}
              style={{ marginRight: 8, width: 200 }}
            >
              {subCategoryList.map((item) => (
                <Option key={item.id} value={item.id!}>
                  {item.categoryName}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem name="skuIds" label="SKUID">
            <Input
              placeholder="多个SKU时请用英文逗号隔开"
              onChange={(value) => {
                // 实时校验SKU ID格式
                if (value) {
                  // 检查是否包含中文逗号
                  if (value.includes('，')) {
                    LzMsg.error('请使用英文逗号(,)分隔多个SKUID');
                    return;
                  }
                  // 检查格式：允许数字、字母、下划线、连字符，用英文逗号分隔
                  const skuPattern = /^[a-zA-Z0-9_-]+(,[a-zA-Z0-9_-]+)*$/;
                  if (!skuPattern.test(value.trim())) {
                    LzMsg.error('SKUID格式不正确，请使用英文逗号分隔');
                  }
                }
              }}
            />
          </FormItem>
          <FormItem name="dateRange" label="日期">
            <RangePicker
              format="YYYY-MM-DD"
              style={{ width: 150 }}
              onChange={(value) => {
                if (value && value.length === 2) {
                  // 更新参数中的日期
                  setParams({
                    ...params,
                    startTime: value[0].format('YYYY-MM-DD'),
                    endTime: value[1].format('YYYY-MM-DD'),
                  });
                }
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
            <Button onClick={exportfn}>导出</Button>
            <Button onClick={exportAllfn}>全量导出</Button>
          </FormItem>
          <FormItem colon={false}></FormItem>
        </Form>
      </LzPanel>

      <LzPanel>
        <Table.StickyLock
          dataSource={listData}
          loading={loading}
          columns={columns}
          primaryKey="skuPair"
        ></Table.StickyLock>
        <LzPagination
          total={pageInfo.total}
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          onChange={(e) => {
            const newParams = {
              ...params,
              pageNum: e.pageNum,
              pageSize: e.pageSize,
            };
            setParams(newParams);
            getList({ ...newParams });
          }}
        />
      </LzPanel>
    </div>
  );
};
