import React from 'react';
import { getActivityPrizeDetail } from '@/api/common';
import { Message, Table } from '@alifd/next';
import { ActivityPrizeDetailInfoResponse } from '@/api/types';

export default ({ id }) => {
  const [data, setData] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [column, setColumn] = React.useState<any[]>([{}]);
  type ActivityPrizeDetailInfoResponseExtend = ActivityPrizeDetailInfoResponse & {
    daySendCount: number | string;
  };
  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await getActivityPrizeDetail({ activityId: id });
      res.activityPrizeDetailResponseList &&
        res.activityPrizeDetailResponseList.forEach((item: ActivityPrizeDetailInfoResponseExtend) => {
          if (item.dayLimit === '无限制' || item.dayLimit === '0') {
            item.daySendCount = '-' as any;
            item.daySurplusCount = '-';
          }
          if (item.prizeType === '积分') {
            item.prizeKey = '-';
          } else if (item.prizeType === '优惠券' && item.prizeKey === '') {
            item.prizeKey = '-';
          }
          if (!item.prizeKey) {
            item.prizeKey = '-';
          }
          if (!item.prizeType) {
            item.prizeType = '-';
          }
        });
      setData(res.activityPrizeDetailResponseList || []);
      setColumn(res.tableList || []);
      setLoading(false);
    } catch (e) {
      setLoading(false);
      Message.error(e.message);
    }
  };

  React.useEffect(() => {
    fetchData().then();
  }, []);
  return (
    <div>
      <Table dataSource={data} style={{ marginTop: '15px' }} loading={loading}>
        {column.map((item, index) => {
          return <Table.Column key={index} title={item.label} dataIndex={item.value} />;
        })}
      </Table>
    </div>
  );
};
