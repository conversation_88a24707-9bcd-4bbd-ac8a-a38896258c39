import React, { useEffect, useReducer, useState } from 'react';
import { Button, Field, Form, Table, Input, Select, Upload, DatePicker2 } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import { skiiCheckGetList } from '@/api/dz';
import LzPanel from '@/components/LzPanel';
import { FormLayout } from '@/types/Form';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const Option = Select.Option;
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState<any>([]);

  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    orderIds: '',
  });
  const getList = async (query: any) => {
    const postData = {
      ...params,
      ...query,
    };
    try {
      setLoading(true);
      const res = await skiiCheckGetList({ ...postData });
      // 处理接口返回的数据
      if (res) {
        setListData((res as any) || []);
      }
    } catch (e: any) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const handleSubmit = async () => {
    const formData: any = field.getValues();
    if (formData.orderIds) {
      if (formData.orderIds.includes('，')) {
        LzMsg.error('请使用英文逗号(,)分隔多个订单ID');
        return;
      }
      const skuPattern = /^[a-zA-Z0-9_-]+(,[a-zA-Z0-9_-]+)*$/;
      if (!skuPattern.test(formData.orderIds.trim())) {
        LzMsg.error('订单ID格式不正确，请使用英文逗号分隔');
        return;
      }
    }
    const newParams = {
      orderIds: formData.orderIds,
    };
    setParams(newParams);
    getList(newParams);
  };

  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem name="orderIds" label="订单ID">
            <Input
              placeholder="多个订单时请用英文逗号隔开"
              onChange={(value) => {
                // 实时校验SKU ID格式
                if (value) {
                  // 检查是否包含中文逗号
                  if (value.includes('，')) {
                    LzMsg.error('请使用英文逗号(,)分隔多个SKUID');
                    return;
                  }
                  // 检查格式：允许数字、字母、下划线、连字符，用英文逗号分隔
                  const skuPattern = /^[a-zA-Z0-9_-]+(,[a-zA-Z0-9_-]+)*$/;
                  if (!skuPattern.test(value.trim())) {
                    LzMsg.error('订单ID格式不正确，请使用英文逗号分隔');
                  }
                }
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
          </FormItem>
        </Form>
      </LzPanel>

      <LzPanel>
        <Table dataSource={listData} loading={loading}>
          <Table.Column align="center" title="营销PIN" dataIndex="cjyxPin" />
          <Table.Column align="center" title="首次访问活动时间" dataIndex="firstVisitTime" />
          <Table.Column align="center" title="是否参与过活动" dataIndex="joinFlag" />
          <Table.Column align="center" title="当前是否已开奖" dataIndex="lotteryDraw" />
          <Table.Column align="center" title="memberId" dataIndex="memberId" />
          <Table.Column align="center" title="昵称" dataIndex="nickName" />
          <Table.Column align="center" title="订单号" dataIndex="orderId" />
          <Table.Column align="center" title="是否报名抽奖" dataIndex="signUp" />
          <Table.Column align="center" title="报名抽奖时间" dataIndex="signUpTime" />
        </Table>
      </LzPanel>
    </div>
  );
};
