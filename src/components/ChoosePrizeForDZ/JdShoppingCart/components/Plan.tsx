import * as React from 'react';
import { useEffect, useState } from 'react';
import { Button, Dialog, Pagination, Table } from '@alifd/next';
import styles from '../index.module.scss';
import SearchForm from './SearchForm';
import format from '@/utils/format';
import { getResPrizeShoppingCardListByPage } from '@/api/sendShoppingCard';
import CreatePlan from './CreatePlan';
import dayjs from 'dayjs';

const defaultPage = { pageSize: 10, pageNum: 1, total: 0 };
export default ({ onSubmit }: any) => {
  const [pageInfo, setPageInfo] = useState({ ...defaultPage });
  const [searchData, setSearchData] = useState({});
  const [datalist, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [planDialog, setPlanDialog] = useState(false);

  // 加载列表
  const loadPageList = (page) => {
    setLoading(true);
    getResPrizeShoppingCardListByPage({ ...searchData, pageNo: page.pageNum, pageSize: page.pageSize, ...page })
      .then((res) => {
        res?.data?.forEach((item: any) => {
          if (dayjs().isBefore(dayjs(item.startDate))) {
            item.planStatus = 1;
          } else if (dayjs().isAfter(dayjs(item.endDate))) {
            item.planStatus = 3;
          } else {
            item.planStatus = 2;
          }
        });
        setList(res.data || []);
        pageInfo.total = res?.page?.total as any;
        pageInfo.pageNum = res?.page?.pageNo as any;
        pageInfo.pageSize = res?.page?.pageSize as any;
        setPageInfo(pageInfo);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 分页事件
  const pageChangeHandler = (pageNum: number) => {
    pageInfo.pageNum = pageNum;
    loadPageList(pageInfo);
  };
  const handlePageSizeChange = (pageSize: number) => {
    pageInfo.pageSize = pageSize;
    pageInfo.pageNum = 1;
    loadPageList(pageInfo);
  };

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]) => {
    const selectRow = datalist.find((o: any) => selectKey.some((p: any) => p === o.planId));
    selectRow.principal = ((parseFloat(selectRow.principal) * 100) / 10000).toFixed(2);
    selectRow.amount = ((parseFloat(selectRow.amount) * 100) / 10000).toFixed(2);
    onSubmit(selectRow);
  };

  // 选择器
  const rowSelection: any = {
    mode: 'single',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
    getProps: (record: { planId: string; quantityRemain: number; planStatus: number }) => {
      return {
        disabled: record.quantityRemain <= 0 || record.planStatus === 3,
      };
    },
  };

  // 当行点击的时候
  const onRowClick = (record: any) => {
    // 预留，如果要求点击行就选择的话，就解开这行
    // onSubmit(record);
  };
  const onSearch = (formData: any) => {
    setSearchData(formData);
    loadPageList({ ...formData, ...defaultPage });
  };
  const parsePointResListStatus = (status: number) => {
    switch (status) {
      case 1:
        return '待生效';
      case 2:
        return '已生效';
      case 3:
        return '已过期';
      default:
        return '已过期';
    }
  };
  const parsePointResListStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return 'table-status-WARNING_COLOR';
      case 2:
        return 'table-status-SUCCESS_COLOR';
      default:
        return 'table-status-LIGHT_GRAY';
    }
  };

  const createPlanSubmit = () => {
    loadPageList(defaultPage);
    setPlanDialog(false);
  };
  const toECardList = () => {
    setPlanDialog(true);
  };
  useEffect(() => {
    loadPageList(defaultPage);
  }, []);

  return (
    <div className={styles.PropertyJdBeanPlan}>
      <SearchForm onSearch={onSearch} />
      {/* <div className={styles.reminderBox}>
        <div />
        <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={toECardList}>
          新建购物卡计划 &gt;
        </Button>
      </div> */}
      <Table.StickyLock
        dataSource={datalist}
        primaryKey="planId"
        fixedHeader
        maxBodyHeight={500}
        loading={loading}
        onRowClick={onRowClick}
        rowSelection={rowSelection}
      >
        <Table.Column width={180} title="计划名称" lock="left" dataIndex="planName" />
        <Table.Column title="计划Id" width={180} dataIndex="planId" />
        <Table.Column width={100} title="购物卡ID" dataIndex="shoppingCardId" />

        <Table.Column
          width={180}
          title="创建时间"
          cell={(value, index, row) => {
            return <div>{dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')}</div>;
          }}
        />
        <Table.Column
          width={200}
          title="购物卡有效期"
          cell={(value, index, activity) => (
            <div>
              <p>起：{dayjs(activity.startDate).format('YYYY-MM-DD HH:mm:ss')}</p>
              <p>止：{dayjs(activity.endDate).format('YYYY-MM-DD HH:mm:ss')}</p>
            </div>
          )}
        />
        <Table.Column
          width={100}
          title="本金(元)"
          dataIndex="principal"
          cell={(data, index, row) => {
            return <div>{((parseFloat(row.principal) * 100) / 10000).toFixed(2)}</div>;
          }}
        />
        <Table.Column
          width={100}
          title="面值(元)"
          dataIndex="amount"
          cell={(data, index, row) => {
            return <div>{((parseFloat(row.amount) * 100) / 10000).toFixed(2)}</div>;
          }}
        />
        <Table.Column
          width={105}
          title="可用数量(张)"
          cell={(value, index, record) => {
            return (
              <div style={{ textAlign: 'center' }}>
                <div>{record.quantityRemain}</div>
              </div>
            );
          }}
        />
        <Table.Column
          width={100}
          title="时效状态"
          cell={(value, index, data) => (
            <span className={styles[parsePointResListStatusColor(data.planStatus)]}>
              {parsePointResListStatus(data.planStatus)}
            </span>
          )}
        />
      </Table.StickyLock>
      <Pagination
        shape="arrow-only"
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        total={pageInfo.total}
        current={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        pageSizeList={[5, 10, 30, 50, 100]}
        totalRender={(total) => `共${total}条`}
        onPageSizeChange={handlePageSizeChange}
        onChange={pageChangeHandler}
        className={styles.pagination}
      />
      <Dialog
        title="新建E卡计划"
        footer={false}
        shouldUpdatePosition
        visible={planDialog}
        onClose={() => setPlanDialog(false)}
      >
        <CreatePlan handleCancel={() => setPlanDialog(false)} handleSubmit={createPlanSubmit} />
      </Dialog>
    </div>
  );
};
